import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

export const TestMarkdown: React.FC = () => {
  const testContent = `
# Test Heading

This is a test of **bold text** and *italic text*.

## Subheading

Here's a list:
- Item 1
- Item 2
- **Bold item**

### Another section

Some more text with **bold formatting** to test.

**TRAFFIC COLLISION SUMMARY** - This should be bold.

**Date, Time, and Location of the Accident** - This should also be bold.
`;

  return (
    <div className="p-4 border border-gray-300 rounded">
      <h3 className="text-lg font-bold mb-4">Markdown Test Component</h3>
      
      <div className="mb-4">
        <h4 className="font-medium mb-2">Raw Content:</h4>
        <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
          {testContent}
        </pre>
      </div>
      
      <div className="mb-4">
        <h4 className="font-medium mb-2">Rendered with ReactMarkdown:</h4>
        <div className="border border-gray-200 p-4 rounded bg-white">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              strong: ({ children }) => (
                <strong className="font-bold text-gray-900 bg-yellow-200">
                  {children}
                </strong>
              ),
              em: ({ children }) => (
                <em className="italic text-blue-600">
                  {children}
                </em>
              ),
              h1: ({ children }) => (
                <h1 className="text-2xl font-bold text-red-600 mb-4">
                  {children}
                </h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-xl font-bold text-green-600 mb-3">
                  {children}
                </h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-lg font-bold text-purple-600 mb-2">
                  {children}
                </h3>
              ),
              p: ({ children }) => (
                <p className="mb-4 text-gray-700">
                  {children}
                </p>
              ),
              ul: ({ children }) => (
                <ul className="list-disc list-inside mb-4 text-gray-700">
                  {children}
                </ul>
              ),
              li: ({ children }) => (
                <li className="mb-1">
                  {children}
                </li>
              )
            }}
          >
            {testContent}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};
