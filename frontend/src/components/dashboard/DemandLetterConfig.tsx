/**
 * Demand Letter Configuration Component
 */

import React, { useState } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { FileText, Settings, DollarSign } from 'lucide-react';

interface DemandLetterConfigProps {
  onGenerate: (config: DemandLetterConfig) => void;
  isGenerating?: boolean;
  hasAnalysisResults?: boolean;
}

export interface DemandLetterConfig {
  // Basic case information
  selectedState: string;
  caseType: string;
  liabilityType: string;
  liabilityAccepted: boolean;
  demandAmount: string;
  customAmount?: string;
  
  // Customization options
  tone: string;
  length: string;
  emphasis: {
    medical_damages: number;
    quality_of_life: number;
    economic_losses: number;
    liability: number;
  };
  
  // Sections to include
  includedSections: {
    facts: boolean;
    liability: boolean;
    damages: boolean;
    medicalTreatment: boolean;
    futureMedicalExpenses: boolean;
    impactOnLifestyle: boolean;
    generalDamages: boolean;
    visualEvidence: boolean;
    conclusion: boolean;
  };
}

const US_STATES = [
  'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut',
  'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
  'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan',
  'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
  'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
  'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
  'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia',
  'Wisconsin', 'Wyoming'
];

const CASE_TYPES = [
  'Traffic Collision',
  'Premises Liability',
  'Slip & Fall',
  'Dog Bite',
  'Product Liability',
  'Medical Malpractice',
  'Workplace Injury',
  'Assault & Battery'
];

const LIABILITY_TYPES = [
  'Bodily Injury Liability',
  'Uninsured Motorist',
  'Under-Insured Motorist',
  'Property Damage Liability',
  'Personal Injury Protection'
];

export const DemandLetterConfig: React.FC<DemandLetterConfigProps> = ({
  onGenerate,
  isGenerating = false,
  hasAnalysisResults = false
}) => {
  const [config, setConfig] = useState<DemandLetterConfig>({
    selectedState: 'California',
    caseType: 'Traffic Collision',
    liabilityType: 'Bodily Injury Liability',
    liabilityAccepted: false,
    demandAmount: 'Policy Limit',
    tone: 'firm',
    length: 'standard',
    emphasis: {
      medical_damages: 3,
      quality_of_life: 3,
      economic_losses: 3,
      liability: 3
    },
    includedSections: {
      facts: true,
      liability: true,
      damages: true,
      medicalTreatment: true,
      futureMedicalExpenses: false,
      impactOnLifestyle: true,
      generalDamages: true,
      visualEvidence: false,
      conclusion: true
    }
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleConfigChange = (key: keyof DemandLetterConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleEmphasisChange = (key: keyof DemandLetterConfig['emphasis'], value: number) => {
    setConfig(prev => ({
      ...prev,
      emphasis: {
        ...prev.emphasis,
        [key]: value
      }
    }));
  };

  const handleSectionChange = (key: keyof DemandLetterConfig['includedSections'], value: boolean) => {
    setConfig(prev => ({
      ...prev,
      includedSections: {
        ...prev.includedSections,
        [key]: value
      }
    }));
  };

  const canGenerate = hasAnalysisResults && config.selectedState && config.caseType && config.liabilityType;

  return (
    <Card>
      <Card.Header>
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-primary-400 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">
            Demand Letter Configuration
          </h3>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Configure your demand letter settings and generate professional legal documents
        </p>
      </Card.Header>
      <Card.Body>
        <div className="space-y-6">
          {/* Basic Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* State Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select State
              </label>
              <select
                value={config.selectedState}
                onChange={(e) => handleConfigChange('selectedState', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
              >
                {US_STATES.map(state => (
                  <option key={state} value={state}>{state}</option>
                ))}
              </select>
            </div>

            {/* Case Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Case Type
              </label>
              <select
                value={config.caseType}
                onChange={(e) => handleConfigChange('caseType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
              >
                {CASE_TYPES.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Liability Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Choose Liability Type
              </label>
              <select
                value={config.liabilityType}
                onChange={(e) => handleConfigChange('liabilityType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
              >
                {LIABILITY_TYPES.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Liability Accepted */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Liability Accepted
              </label>
              <select
                value={config.liabilityAccepted ? 'Yes' : 'No'}
                onChange={(e) => handleConfigChange('liabilityAccepted', e.target.value === 'Yes')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
              >
                <option value="No">No</option>
                <option value="Yes">Yes</option>
              </select>
            </div>
          </div>

          {/* Demand Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Set the Demand Amount
            </label>
            <div className="flex space-x-4">
              <div className="flex-1">
                <select
                  value={config.demandAmount}
                  onChange={(e) => handleConfigChange('demandAmount', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
                >
                  <option value="Policy Limit">Policy Limit</option>
                  <option value="Custom">Enter Amount</option>
                </select>
              </div>
              {config.demandAmount === 'Custom' && (
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Enter amount (e.g., $100,000)"
                    value={config.customAmount || ''}
                    onChange={(e) => handleConfigChange('customAmount', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Advanced Settings Toggle */}
          <div className="border-t pt-4">
            <Button
              variant="ghost"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center"
            >
              <Settings className="w-4 h-4 mr-2" />
              {showAdvanced ? 'Hide' : 'Show'} Advanced Settings
            </Button>
          </div>

          {/* Advanced Settings */}
          {showAdvanced && (
            <div className="space-y-6 bg-gray-50 p-4 rounded-lg">
              {/* Tone and Length */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tone
                  </label>
                  <select
                    value={config.tone}
                    onChange={(e) => handleConfigChange('tone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
                  >
                    <option value="firm">Firm</option>
                    <option value="neutral">Neutral</option>
                    <option value="conciliatory">Conciliatory</option>
                    <option value="aggressive">Aggressive</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Length
                  </label>
                  <select
                    value={config.length}
                    onChange={(e) => handleConfigChange('length', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
                  >
                    <option value="basic">Basic (Concise)</option>
                    <option value="standard">Standard (Balanced)</option>
                    <option value="large">Large (Comprehensive)</option>
                  </select>
                </div>
              </div>

              {/* Content Emphasis */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Content Emphasis (1 = Low, 5 = High)
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(config.emphasis).map(([key, value]) => (
                    <div key={key}>
                      <label className="block text-xs text-gray-600 mb-1">
                        {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </label>
                      <input
                        type="range"
                        min="1"
                        max="5"
                        value={value}
                        onChange={(e) => handleEmphasisChange(key as keyof DemandLetterConfig['emphasis'], parseInt(e.target.value))}
                        className="w-full"
                      />
                      <div className="text-xs text-gray-500 text-center">{value}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Sections to Include */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Sections to Include
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {Object.entries(config.includedSections).map(([key, value]) => (
                    <label key={key} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => handleSectionChange(key as keyof DemandLetterConfig['includedSections'], e.target.checked)}
                        className="rounded border-gray-300 text-primary-400 focus:ring-primary-400"
                      />
                      <span className="text-sm text-gray-700">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Generate Button */}
          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {!hasAnalysisResults ? (
                  <span className="text-amber-600">⚠️ Generate analysis first to enable demand letter creation</span>
                ) : (
                  <span className="text-green-600">✅ Ready to generate demand letter (5 tokens)</span>
                )}
              </div>
              <Button
                variant="primary"
                size="lg"
                disabled={!canGenerate || isGenerating}
                onClick={() => onGenerate(config)}
                className="flex items-center"
              >
                <DollarSign className="w-4 h-4 mr-2" />
                {isGenerating ? 'Generating...' : 'Generate Demand Letter (5 tokens)'}
              </Button>
            </div>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};
