/**
 * Preferences configuration panel
 */

import React, { useState, useEffect } from 'react';
import { Save, RotateCcw, ChevronDown, ChevronRight } from 'lucide-react';
import { usePreferencesStore } from '../../store/preferencesStore';
import { useDocumentClassification } from '../../hooks/useDocumentClassification';
import { PoliceReportSection } from './PoliceReportSection';
import { MedicalPreferencesSection } from './MedicalPreferencesSection';
import { ImageAnalysisSection } from './ImageAnalysisSection';
import { 
  AnalysisType, 
  DetailLevel, 
  ContentEmphasis, 
  AnalysisStyle,
  LetterLength,
  ToneSetting,
  DemandLetterEmphasis,
  DemandLetterSection
} from '../../types/preferences.types';
import { Button } from '../ui/Button';
import { Checkbox } from '../ui/Checkbox';

export const PreferencesPanel: React.FC = () => {
  const [expandedSections, setExpandedSections] = useState({
    policeReport: false,
    medicalAnalysis: false,
    imageAnalysis: false,
    demandLetter: false
  });

  // Get document classification summary
  const { summary } = useDocumentClassification();

  // Auto-expand sections when relevant documents are detected
  useEffect(() => {
    if (summary) {
      setExpandedSections(prev => ({
        ...prev,
        policeReport: summary.has_police_reports,
        medicalAnalysis: summary.has_medical_records || summary.has_billing_statements,
        demandLetter: summary.has_medical_records || summary.has_billing_statements
      }));
    }
  }, [summary]);

  const {
    preferences,
    analysisTypes,
    isLoading,
    setClientName,
    toggleAnalysisType,
    setDetailLevel,
    setContentEmphasis,
    setAnalysisStyle,
    setDemandLetterLength,
    setDemandLetterTone,
    setDemandLetterEmphasis,
    toggleDemandLetterSection,
    updatePreferences,
    resetPreferences
  } = usePreferencesStore();

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleSave = async () => {
    try {
      await updatePreferences({
        analysis: preferences.analysis,
        demand_letter: preferences.demand_letter
      });
    } catch (error) {
      // Error is handled by the store
    }
  };

  const handleReset = async () => {
    try {
      await resetPreferences();
    } catch (error) {
      // Error is handled by the store
    }
  };

  const analysisTypeOptions = analysisTypes ? Object.entries(analysisTypes.analysis_types) : [];

  return (
    <div className="space-y-6">
      {/* Client Information */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
        <div>
          <label className="form-label">Client Name</label>
          <input
            type="text"
            value={preferences.client_name || ''}
            onChange={(e) => setClientName(e.target.value)}
            className="form-input"
            placeholder="Enter client name"
          />
        </div>
      </div>

      {/* Police Report Analysis */}
      <div className="border border-gray-200 rounded-lg">
        <button
          onClick={() => toggleSection('policeReport')}
          className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors duration-200"
        >
          <div className="flex items-center">
            <h3 className="text-lg font-medium text-gray-900">Police Report Analysis</h3>
            {summary?.has_police_reports && (
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Documents detected
              </span>
            )}
          </div>
          {expandedSections.policeReport ? (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-400" />
          )}
        </button>

        {expandedSections.policeReport && (
          <div className="border-t border-gray-200">
            <PoliceReportSection
              onGenerate={(config) => {
                // Handle police report analysis generation
                console.log('Police report analysis config:', config);
              }}
              isGenerating={false}
              enabled={summary?.has_police_reports || false}
            />
          </div>
        )}
      </div>

      {/* Medical Analysis */}
      <div className="border border-gray-200 rounded-lg">
        <button
          onClick={() => toggleSection('medicalAnalysis')}
          className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors duration-200"
        >
          <div className="flex items-center">
            <h3 className="text-lg font-medium text-gray-900">Medical Analysis</h3>
            {(summary?.has_medical_records || summary?.has_billing_statements) && (
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Documents detected
              </span>
            )}
          </div>
          {expandedSections.medicalAnalysis ? (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-400" />
          )}
        </button>

        {expandedSections.medicalAnalysis && (
          <div className="border-t border-gray-200">
            <MedicalPreferencesSection
              onGenerate={(config) => {
                // Handle medical analysis generation
                console.log('Medical analysis config:', config);
              }}
              isGenerating={false}
              enabled={summary?.has_medical_records || summary?.has_billing_statements || false}
            />
          </div>
        )}
      </div>

      {/* Image Analysis */}
      <div className="border border-gray-200 rounded-lg">
        <button
          onClick={() => toggleSection('imageAnalysis')}
          className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors duration-200"
        >
          <div className="flex items-center">
            <h3 className="text-lg font-medium text-gray-900">Image Analysis</h3>
            {/* Will show when images are detected */}
          </div>
          {expandedSections.imageAnalysis ? (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-400" />
          )}
        </button>

        {expandedSections.imageAnalysis && (
          <div className="border-t border-gray-200">
            <ImageAnalysisSection
              onGenerate={(config) => {
                // Handle image analysis generation
                console.log('Image analysis config:', config);
              }}
              isGenerating={false}
              enabled={false} // Will be true when images are detected
            />
          </div>
        )}
      </div>

      {/* Demand Letter Preferences */}
      <div className="border border-gray-200 rounded-lg">
        <button
          onClick={() => toggleSection('demandLetter')}
          className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors duration-200"
        >
          <h3 className="text-lg font-medium text-gray-900">Demand Letter Configuration</h3>
          {expandedSections.demandLetter ? (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronRight className="w-5 h-5 text-gray-400" />
          )}
        </button>

        {expandedSections.demandLetter && (
          <div className="px-4 pb-4 space-y-6">
            {/* Letter Sections */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Include Sections
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {Object.values(DemandLetterSection).map((section) => (
                  <Checkbox
                    key={section}
                    label={section.charAt(0).toUpperCase() + section.slice(1).replace('_', ' ')}
                    checked={preferences.demand_letter.sections.includes(section)}
                    onChange={() => toggleDemandLetterSection(section)}
                  />
                ))}
              </div>
            </div>

            {/* Letter Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Letter Length */}
              <div>
                <label className="form-label">Letter Length</label>
                <select
                  value={preferences.demand_letter.length}
                  onChange={(e) => setDemandLetterLength(e.target.value)}
                  className="form-input"
                >
                  <option value={LetterLength.CONCISE}>Concise</option>
                  <option value={LetterLength.STANDARD}>Standard</option>
                  <option value={LetterLength.DETAILED}>Detailed</option>
                </select>
              </div>

              {/* Tone Setting */}
              <div>
                <label className="form-label">Tone</label>
                <select
                  value={preferences.demand_letter.tone}
                  onChange={(e) => setDemandLetterTone(e.target.value)}
                  className="form-input"
                >
                  <option value={ToneSetting.PROFESSIONAL}>Professional</option>
                  <option value={ToneSetting.ASSERTIVE}>Assertive</option>
                  <option value={ToneSetting.DIPLOMATIC}>Diplomatic</option>
                </select>
              </div>

              {/* Content Emphasis */}
              <div>
                <label className="form-label">Content Emphasis</label>
                <select
                  value={preferences.demand_letter.content_emphasis}
                  onChange={(e) => setDemandLetterEmphasis(e.target.value)}
                  className="form-input"
                >
                  <option value={DemandLetterEmphasis.LEGAL}>Legal</option>
                  <option value={DemandLetterEmphasis.MEDICAL}>Medical</option>
                  <option value={DemandLetterEmphasis.FINANCIAL}>Financial</option>
                  <option value={DemandLetterEmphasis.EMOTIONAL}>Emotional</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="secondary"
          onClick={handleReset}
          loading={isLoading}
          icon={<RotateCcw className="w-4 h-4" />}
        >
          Reset to Defaults
        </Button>

        <Button
          variant="primary"
          onClick={handleSave}
          loading={isLoading}
          icon={<Save className="w-4 h-4" />}
        >
          Save Preferences
        </Button>
      </div>
    </div>
  );
};
