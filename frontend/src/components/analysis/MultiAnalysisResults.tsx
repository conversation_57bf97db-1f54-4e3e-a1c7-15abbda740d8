/**
 * Component for displaying multiple analysis results with individual download options
 */

import React, { useState } from 'react';
import { Download, Clock, CheckCircle, AlertCircle, Zap, Eye } from 'lucide-react';
import { Button } from '../ui/Button';
import { <PERSON>down<PERSON>enderer } from '../ui/MarkdownRenderer';
import { multiAnalysisService } from '../../services/multiAnalysisService';
import toast from 'react-hot-toast';

interface MultiAnalysisResultsProps {
  analysisId: string;
  results: Record<string, any>;
  totalTokensUsed: number;
  onClose: () => void;
}

export const MultiAnalysisResults: React.FC<MultiAnalysisResultsProps> = ({
  analysisId,
  results,
  totalTokensUsed,
  onClose
}) => {
  const [downloadingItem, setDownloadingItem] = useState<string | null>(null);
  const [downloadingCombined, setDownloadingCombined] = useState(false);
  const [expandedResult, setExpandedResult] = useState<string | null>(null);

  // Format results for display
  const formattedResults = multiAnalysisService.formatAnalysisResults(results);
  const completedResults = formattedResults.filter(result => result.status === 'completed');
  const failedResults = formattedResults.filter(result => result.status === 'failed');

  // Debug log to check content
  console.log('MultiAnalysisResults - formattedResults:', formattedResults.map(r => ({
    type: r.type,
    contentLength: r.content?.length || 0,
    contentPreview: r.content?.substring(0, 100) + '...'
  })));

  const handleDownloadIndividual = async (analysisType: string, displayName: string) => {
    setDownloadingItem(analysisType);
    try {
      await multiAnalysisService.downloadIndividualResult(analysisId, analysisType);
      toast.success(`${displayName} downloaded successfully`);
    } catch (error: any) {
      toast.error(`Failed to download ${displayName}: ${error.message}`);
    } finally {
      setDownloadingItem(null);
    }
  };

  const handleDownloadCombined = async () => {
    setDownloadingCombined(true);
    try {
      await multiAnalysisService.downloadCombinedResults(analysisId);
      toast.success('Combined analysis report downloaded successfully');
    } catch (error: any) {
      toast.error(`Failed to download combined report: ${error.message}`);
    } finally {
      setDownloadingCombined(false);
    }
  };

  const toggleExpanded = (type: string) => {
    setExpandedResult(expandedResult === type ? null : type);
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Analysis Results</h2>
              <p className="text-sm text-gray-600 mt-1">
                {completedResults.length} of {formattedResults.length} analyses completed
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Zap className="w-4 h-4 text-blue-500 mr-1" />
                </div>
                <div className="text-sm font-medium text-blue-900">{totalTokensUsed}</div>
                <div className="text-xs text-blue-600">Tokens Used</div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Download Combined Button */}
          {completedResults.length > 1 && (
            <div className="mb-6">
              <Button
                onClick={handleDownloadCombined}
                disabled={downloadingCombined}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
              >
                {downloadingCombined ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Downloading Combined Report...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    Download Combined Report ({completedResults.length} analyses)
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Individual Results */}
          <div className="space-y-4">
            {formattedResults.map((result) => (
              <div
                key={result.type}
                className={`border rounded-lg p-4 transition-all duration-200 ${
                  result.status === 'completed'
                    ? 'border-green-200 bg-green-50'
                    : 'border-red-200 bg-red-50'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    {result.status === 'completed' ? (
                      <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-500 mr-3" />
                    )}
                    <div>
                      <h3 className="font-medium text-gray-900">{result.displayName}</h3>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <Clock className="w-3 h-3 mr-1" />
                        {formatTimestamp(result.timestamp)}
                        <span className="mx-2">•</span>
                        <Zap className="w-3 h-3 mr-1" />
                        {result.tokensUsed} token{result.tokensUsed !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {result.status === 'completed' && (
                      <>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => toggleExpanded(result.type)}
                          className="text-gray-600 hover:text-gray-800"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          {expandedResult === result.type ? 'Hide' : 'Preview'}
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleDownloadIndividual(result.type, result.displayName)}
                          disabled={downloadingItem === result.type}
                          className="bg-turquoise hover:bg-turquoise/90 text-white"
                        >
                          {downloadingItem === result.type ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          ) : (
                            <Download className="w-4 h-4" />
                          )}
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {/* Content Preview */}
                {result.status === 'completed' && (
                  <div className="mt-3">
                    <div className="text-sm text-gray-700 bg-white p-3 rounded border">
                      {expandedResult === result.type ? (
                        <MarkdownRenderer
                          content={result.content}
                          className="prose max-w-none text-sm"
                        />
                      ) : (
                        <MarkdownRenderer
                          content={truncateContent(result.content)}
                          className="prose max-w-none text-sm"
                        />
                      )}
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {result.status === 'failed' && (
                  <div className="mt-3">
                    <div className="text-sm text-red-700 bg-red-100 p-3 rounded border border-red-200">
                      {result.content}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Summary */}
          {formattedResults.length > 0 && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
              <h4 className="font-medium text-gray-900 mb-2">Analysis Summary</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-medium text-green-600">{completedResults.length}</div>
                  <div className="text-gray-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="font-medium text-red-600">{failedResults.length}</div>
                  <div className="text-gray-600">Failed</div>
                </div>
                <div className="text-center">
                  <div className="font-medium text-blue-600">{totalTokensUsed}</div>
                  <div className="text-gray-600">Tokens Used</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <Button
              onClick={onClose}
              variant="secondary"
              className="text-gray-600 hover:text-gray-800"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
