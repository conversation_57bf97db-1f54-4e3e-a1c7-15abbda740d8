/**
 * Service for handling multiple analysis workflows with token management
 */

import { apiClient } from './api';
import { AnalysisType } from '../types/preferences.types';

export interface AnalysisCostCalculation {
  total_cost: number;
  analysis_count: number;
  cost_per_analysis: number;
  tokens_remaining: number;
  sufficient_tokens: boolean;
  selected_analyses: string[];
  message: string;
}

export interface MultiAnalysisProgress {
  analysis_id: string;
  status: 'started' | 'processing' | 'completed' | 'failed';
  progress: number;
  results: Record<string, any>;
  completed_analyses: string[];
  tokens_used?: number;
  remaining_tokens?: number;
  total_tokens_used?: number;
  error?: string;
}

export interface IndividualAnalysisResult {
  content: string;
  status: 'completed' | 'failed';
  tokens_used: number;
  timestamp: number;
  analysis_type: string;
}

class MultiAnalysisService {
  /**
   * Calculate the total cost for selected analyses
   */
  async calculateCost(selectedAnalyses: AnalysisType[]): Promise<AnalysisCostCalculation> {
    try {
      const response = await apiClient.post('/analysis/calculate-cost', {
        selected_analyses: selectedAnalyses
      });
      
      return response.data;
    } catch (error: any) {
      console.error('Error calculating analysis cost:', error);
      throw new Error(error.response?.data?.detail || 'Failed to calculate analysis cost');
    }
  }

  /**
   * Start multiple analysis workflow using the existing direct analysis system
   */
  async startMultipleAnalysis(
    documentIds: string[],
    selectedAnalyses: AnalysisType[],
    preferences: any
  ): Promise<{ analysis_id: string; message: string }> {
    try {
      // Get files from sessionStorage
      const storedFiles = JSON.parse(sessionStorage.getItem('casebuilder-files') || '[]');

      if (storedFiles.length === 0) {
        throw new Error('No files found in memory');
      }

      // Format files for the API
      const files = storedFiles.map((file: any) => ({
        name: file.name,
        content: file.content, // base64 content
        type: file.type
      }));

      // Create a unique analysis ID for this multi-analysis session
      const analysisId = `multi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Store the multi-analysis request in sessionStorage for processing
      const multiAnalysisData = {
        id: analysisId,
        selectedAnalyses,
        preferences,
        files,
        status: 'pending',
        results: {},
        startedAt: new Date().toISOString()
      };

      sessionStorage.setItem(`multi-analysis-${analysisId}`, JSON.stringify(multiAnalysisData));

      return {
        analysis_id: analysisId,
        message: 'Multi-analysis started successfully'
      };
    } catch (error: any) {
      console.error('Error starting multiple analysis:', error);
      throw new Error(error.response?.data?.detail || 'Failed to start analysis');
    }
  }

  /**
   * Get analysis progress and status using direct analysis system
   */
  async getAnalysisProgress(analysisId: string): Promise<MultiAnalysisProgress> {
    try {
      // Get the multi-analysis data from sessionStorage
      const multiAnalysisData = JSON.parse(
        sessionStorage.getItem(`multi-analysis-${analysisId}`) || '{}'
      );

      if (!multiAnalysisData.id) {
        throw new Error('Multi-analysis data not found');
      }

      // If already completed, return the stored results
      if (multiAnalysisData.status === 'completed') {
        return {
          analysis_id: analysisId,
          status: 'completed',
          progress: 100,
          results: multiAnalysisData.results,
          tokens_used: multiAnalysisData.tokensUsed || 0,
          remaining_tokens: multiAnalysisData.remainingTokens || 0,
          completed_analyses: multiAnalysisData.completedAnalyses || []
        };
      }

      // If processing hasn't started, start it
      if (multiAnalysisData.status === 'pending') {
        multiAnalysisData.status = 'processing';
        multiAnalysisData.currentAnalysisIndex = 0;
        multiAnalysisData.results = {};
        multiAnalysisData.tokensUsed = 0;
        multiAnalysisData.completedAnalyses = [];
        sessionStorage.setItem(`multi-analysis-${analysisId}`, JSON.stringify(multiAnalysisData));
      }

      // Process analyses one by one using the direct analysis system
      const { selectedAnalyses, preferences, files } = multiAnalysisData;
      const currentIndex = multiAnalysisData.currentAnalysisIndex || 0;

      if (currentIndex < selectedAnalyses.length) {
        const currentAnalysisType = selectedAnalyses[currentIndex];

        // Check if this analysis is already completed
        if (!multiAnalysisData.results[currentAnalysisType]) {
          try {
            // Call the direct analysis endpoint that already works
            const directResponse = await apiClient.post('/analysis/direct', {
              analysis_type: currentAnalysisType,
              files: files,
              preferences: preferences
            });

            // Store the result
            multiAnalysisData.results[currentAnalysisType] = {
              type: currentAnalysisType,
              content: directResponse.data.result,
              timestamp: new Date().toISOString(),
              tokensUsed: 1 // Each analysis uses 1 token
            };

            multiAnalysisData.tokensUsed = (multiAnalysisData.tokensUsed || 0) + 1;
            multiAnalysisData.completedAnalyses.push(currentAnalysisType);

          } catch (error) {
            console.error(`Error processing ${currentAnalysisType}:`, error);
            // Store error result
            multiAnalysisData.results[currentAnalysisType] = {
              type: currentAnalysisType,
              content: `Error processing ${currentAnalysisType}: ${error}`,
              timestamp: new Date().toISOString(),
              tokensUsed: 0,
              error: true
            };
          }
        }

        // Move to next analysis
        multiAnalysisData.currentAnalysisIndex = currentIndex + 1;
        sessionStorage.setItem(`multi-analysis-${analysisId}`, JSON.stringify(multiAnalysisData));
      }

      // Check if all analyses are completed
      const completedCount = Object.keys(multiAnalysisData.results).length;
      const totalCount = selectedAnalyses.length;
      const progress = Math.round((completedCount / totalCount) * 100);

      if (completedCount >= totalCount) {
        multiAnalysisData.status = 'completed';
        sessionStorage.setItem(`multi-analysis-${analysisId}`, JSON.stringify(multiAnalysisData));
      }

      return {
        analysis_id: analysisId,
        status: multiAnalysisData.status,
        progress: progress,
        results: multiAnalysisData.results,
        tokens_used: multiAnalysisData.tokensUsed || 0,
        remaining_tokens: multiAnalysisData.remainingTokens || 0,
        completed_analyses: multiAnalysisData.completedAnalyses || []
      };

    } catch (error: any) {
      console.error('Error getting analysis progress:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get analysis progress');
    }
  }

  /**
   * Download combined analysis results as Word document
   */
  async downloadCombinedResults(analysisId: string): Promise<void> {
    try {
      // Get the multi-analysis data from sessionStorage
      const multiAnalysisData = JSON.parse(
        sessionStorage.getItem(`multi-analysis-${analysisId}`) || '{}'
      );

      if (!multiAnalysisData.results) {
        throw new Error('No analysis results found');
      }

      // Prepare combined content for export
      const combinedContent = Object.entries(multiAnalysisData.results).map(([type, result]: [string, any]) => ({
        analysis_type: type,
        content: result.content || result
      }));

      const response = await apiClient.post('/analysis/export/word', {
        results: combinedContent,
        title: 'Multi-Analysis Report'
      }, {
        responseType: 'blob'
      });

      // Create download link
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Extract filename from response headers
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'Multi-Analysis-Report.docx';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Error downloading combined results:', error);
      throw new Error(error.response?.data?.detail || 'Failed to download combined results');
    }
  }

  /**
   * Download individual analysis result as Word document
   */
  async downloadIndividualResult(analysisId: string, analysisType: string): Promise<void> {
    try {
      // Get the multi-analysis data from sessionStorage
      const multiAnalysisData = JSON.parse(
        sessionStorage.getItem(`multi-analysis-${analysisId}`) || '{}'
      );

      if (!multiAnalysisData.results || !multiAnalysisData.results[analysisType]) {
        throw new Error(`No analysis result found for ${analysisType}`);
      }

      const result = multiAnalysisData.results[analysisType];
      const content = result.content || result;

      const response = await apiClient.post('/analysis/export/word', {
        results: [{
          analysis_type: analysisType,
          content: content
        }],
        title: this.getAnalysisDisplayName(analysisType)
      }, {
        responseType: 'blob'
      });

      // Create download link
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Extract filename from response headers
      const contentDisposition = response.headers['content-disposition'];
      let filename = `${analysisType.replace(/_/g, '-')}-Analysis.docx`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Error downloading individual result:', error);
      throw new Error(error.response?.data?.detail || 'Failed to download individual result');
    }
  }

  /**
   * Cancel ongoing analysis
   */
  async cancelAnalysis(analysisId: string): Promise<void> {
    try {
      await apiClient.post(`/analysis/${analysisId}/cancel`);
    } catch (error: any) {
      console.error('Error cancelling analysis:', error);
      throw new Error(error.response?.data?.detail || 'Failed to cancel analysis');
    }
  }

  /**
   * Get analysis type display name
   */
  getAnalysisDisplayName(type: string): string {
    const names: { [key: string]: string } = {
      'police_report_summary': 'Police Report Summary',
      'facts_liability': 'Facts & Liability Analysis',
      'in_depth_liability': 'In-Depth Liability Analysis',
      'medical_analysis': 'Medical Analysis',
      'medical_expenses': 'Medical Expenses',
      'future_treatment': 'Future Treatment',
      'analyze_injuries': 'Analyze Injuries',
      'accident_scene': 'Accident Scene',
      'property_damage': 'Property Damage'
    };
    return names[type] || type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
  }

  /**
   * Format analysis results for display
   */
  formatAnalysisResults(results: Record<string, any>): Array<{
    type: string;
    displayName: string;
    content: string;
    status: string;
    tokensUsed: number;
    timestamp: number;
  }> {
    return Object.entries(results).map(([type, result]) => {
      // Handle both old format (string) and new format (object)
      const isNewFormat = typeof result === 'object' && result !== null;
      
      return {
        type,
        displayName: this.getAnalysisDisplayName(type),
        content: isNewFormat ? result.content : result,
        status: isNewFormat ? result.status : 'completed',
        tokensUsed: isNewFormat ? result.tokens_used : 1,
        timestamp: isNewFormat ? result.timestamp : Date.now()
      };
    });
  }

  /**
   * Check if user has sufficient tokens for selected analyses
   */
  async checkSufficientTokens(selectedAnalyses: AnalysisType[]): Promise<{
    sufficient: boolean;
    required: number;
    available: number;
    message: string;
  }> {
    try {
      const costInfo = await this.calculateCost(selectedAnalyses);
      
      return {
        sufficient: costInfo.sufficient_tokens,
        required: costInfo.total_cost,
        available: costInfo.tokens_remaining,
        message: costInfo.sufficient_tokens 
          ? `You have sufficient tokens (${costInfo.tokens_remaining} available, ${costInfo.total_cost} required)`
          : `Insufficient tokens. You need ${costInfo.total_cost} tokens but only have ${costInfo.tokens_remaining} available.`
      };
    } catch (error: any) {
      console.error('Error checking token sufficiency:', error);
      return {
        sufficient: false,
        required: selectedAnalyses.length,
        available: 0,
        message: 'Unable to verify token availability'
      };
    }
  }
}

export const multiAnalysisService = new MultiAnalysisService();
