/**
 * Service for handling multiple analysis workflows with token management
 */

import { apiClient } from './api';
import { AnalysisType } from '../types/preferences.types';

export interface AnalysisCostCalculation {
  total_cost: number;
  analysis_count: number;
  cost_per_analysis: number;
  tokens_remaining: number;
  sufficient_tokens: boolean;
  selected_analyses: string[];
  message: string;
}

export interface MultiAnalysisProgress {
  analysis_id: string;
  status: 'started' | 'processing' | 'completed' | 'failed';
  progress: number;
  results: Record<string, any>;
  completed_analyses: string[];
  tokens_used?: number;
  remaining_tokens?: number;
  total_tokens_used?: number;
  error?: string;
}

export interface IndividualAnalysisResult {
  content: string;
  status: 'completed' | 'failed';
  tokens_used: number;
  timestamp: number;
  analysis_type: string;
}

class MultiAnalysisService {
  /**
   * Calculate the total cost for selected analyses
   */
  async calculateCost(selectedAnalyses: AnalysisType[]): Promise<AnalysisCostCalculation> {
    try {
      const response = await apiClient.post('/analysis/calculate-cost', {
        selected_analyses: selectedAnalyses
      });
      
      return response.data;
    } catch (error: any) {
      console.error('Error calculating analysis cost:', error);
      throw new Error(error.response?.data?.detail || 'Failed to calculate analysis cost');
    }
  }

  /**
   * Start multiple analysis workflow
   */
  async startMultipleAnalysis(
    documentIds: string[],
    selectedAnalyses: AnalysisType[],
    preferences: any
  ): Promise<{ analysis_id: string; message: string }> {
    try {
      const response = await apiClient.post('/analysis/start', {
        document_ids: documentIds,
        selected_analyses: selectedAnalyses,
        preferences
      });
      
      return response.data;
    } catch (error: any) {
      console.error('Error starting multiple analysis:', error);
      throw new Error(error.response?.data?.detail || 'Failed to start analysis');
    }
  }

  /**
   * Get analysis progress and status
   */
  async getAnalysisProgress(analysisId: string): Promise<MultiAnalysisProgress> {
    try {
      const response = await apiClient.get(`/analysis/${analysisId}/status`);
      return response.data;
    } catch (error: any) {
      console.error('Error getting analysis progress:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get analysis progress');
    }
  }

  /**
   * Download combined analysis results as Word document
   */
  async downloadCombinedResults(analysisId: string): Promise<void> {
    try {
      const response = await apiClient.get(`/analysis/${analysisId}/export/word`, {
        responseType: 'blob'
      });

      // Create download link
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Extract filename from response headers
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'Multi-Analysis-Report.docx';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Error downloading combined results:', error);
      throw new Error(error.response?.data?.detail || 'Failed to download combined results');
    }
  }

  /**
   * Download individual analysis result as Word document
   */
  async downloadIndividualResult(analysisId: string, analysisType: string): Promise<void> {
    try {
      const response = await apiClient.get(`/analysis/${analysisId}/export/individual/${analysisType}`, {
        responseType: 'blob'
      });

      // Create download link
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Extract filename from response headers
      const contentDisposition = response.headers['content-disposition'];
      let filename = `${analysisType.replace(/_/g, '-')}-Analysis.docx`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Error downloading individual result:', error);
      throw new Error(error.response?.data?.detail || 'Failed to download individual result');
    }
  }

  /**
   * Cancel ongoing analysis
   */
  async cancelAnalysis(analysisId: string): Promise<void> {
    try {
      await apiClient.post(`/analysis/${analysisId}/cancel`);
    } catch (error: any) {
      console.error('Error cancelling analysis:', error);
      throw new Error(error.response?.data?.detail || 'Failed to cancel analysis');
    }
  }

  /**
   * Get analysis type display name
   */
  getAnalysisDisplayName(type: string): string {
    const names: { [key: string]: string } = {
      'police_report_summary': 'Police Report Summary',
      'facts_liability': 'Facts & Liability Analysis',
      'in_depth_liability': 'In-Depth Liability Analysis',
      'medical_analysis': 'Medical Analysis',
      'medical_expenses': 'Medical Expenses',
      'future_treatment': 'Future Treatment',
      'analyze_injuries': 'Analyze Injuries',
      'accident_scene': 'Accident Scene',
      'property_damage': 'Property Damage'
    };
    return names[type] || type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
  }

  /**
   * Format analysis results for display
   */
  formatAnalysisResults(results: Record<string, any>): Array<{
    type: string;
    displayName: string;
    content: string;
    status: string;
    tokensUsed: number;
    timestamp: number;
  }> {
    return Object.entries(results).map(([type, result]) => {
      // Handle both old format (string) and new format (object)
      const isNewFormat = typeof result === 'object' && result !== null;
      
      return {
        type,
        displayName: this.getAnalysisDisplayName(type),
        content: isNewFormat ? result.content : result,
        status: isNewFormat ? result.status : 'completed',
        tokensUsed: isNewFormat ? result.tokens_used : 1,
        timestamp: isNewFormat ? result.timestamp : Date.now()
      };
    });
  }

  /**
   * Check if user has sufficient tokens for selected analyses
   */
  async checkSufficientTokens(selectedAnalyses: AnalysisType[]): Promise<{
    sufficient: boolean;
    required: number;
    available: number;
    message: string;
  }> {
    try {
      const costInfo = await this.calculateCost(selectedAnalyses);
      
      return {
        sufficient: costInfo.sufficient_tokens,
        required: costInfo.total_cost,
        available: costInfo.tokens_remaining,
        message: costInfo.sufficient_tokens 
          ? `You have sufficient tokens (${costInfo.tokens_remaining} available, ${costInfo.total_cost} required)`
          : `Insufficient tokens. You need ${costInfo.total_cost} tokens but only have ${costInfo.tokens_remaining} available.`
      };
    } catch (error: any) {
      console.error('Error checking token sufficiency:', error);
      return {
        sufficient: false,
        required: selectedAnalyses.length,
        available: 0,
        message: 'Unable to verify token availability'
      };
    }
  }
}

export const multiAnalysisService = new MultiAnalysisService();
