{"name": "casebuilder-ai-frontend", "version": "2.0.0", "description": "CaseBuilder AI - Modern React Frontend", "private": true, "dependencies": {"@types/node": "^16.18.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "axios": "^1.3.0", "clsx": "^1.2.0", "lucide-react": "^0.263.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.0", "react-hook-form": "^7.43.0", "react-hot-toast": "^2.4.0", "react-markdown": "^9.1.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "remark-gfm": "^3.0.1", "tailwind-merge": "^1.14.0", "typescript": "^4.9.5", "web-vitals": "^2.1.0", "zustand": "^4.3.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "@types/jest": "^27.5.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}