"""
Generation router for CaseBuilder AI.
Handles content generation (demand letters, reports, etc.).
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.responses import StreamingResponse
import logging
import uuid
import time
import io
from typing import Dict, Any
import asyncio

from ..models.requests import GenerationRequest
from ..models.responses import (
    GenerationResponse, 
    BaseResponse,
    ProgressResponse
)
from ..services.auth_service import User
from ..services.token_service import token_service
from ..dependencies import get_current_user, get_current_session, validate_session_access
from ..middleware.session import session_store

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/demand-letter", response_model=GenerationResponse)
async def generate_demand_letter(
    generation_request: GenerationRequest,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Generate a demand letter based on analysis results.
    
    Args:
        generation_request: Generation configuration
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        GenerationResponse with generation details
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)

        # Check and deduct tokens for demand generation
        success, remaining_tokens = await token_service.check_and_deduct_tokens(
            user.username,
            'demand_generation'
        )

        if not success:
            available_tokens = await token_service.get_user_tokens(user.username)
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail=f"Insufficient tokens. Available: {available_tokens}, Required: {token_service.DEMAND_GENERATION_TOKEN_COST}"
            )

        logger.info(f"Deducted {token_service.DEMAND_GENERATION_TOKEN_COST} tokens from user {user.username}. Remaining: {remaining_tokens}")

        # Generate generation ID
        generation_id = str(uuid.uuid4())
        current_time = time.time()
        
        # Create generation record
        generation_data = {
            "id": generation_id,
            "type": "demand_letter",
            "preferences": generation_request.preferences.dict(),
            "analysis_results": generation_request.analysis_results,
            "status": "started",
            "progress": 0,
            "content": None,
            "export_url": None,
            "started_at": current_time,
            "completed_at": None,
            "error": None
        }
        
        # Store generation in session
        generations = session_data.get("generations", {})
        generations[generation_id] = generation_data
        
        session_store.update_session(session_id, {
            "generations": generations
        })
        
        # Start background generation
        asyncio.create_task(
            process_generation_background(generation_id, session_id, generation_request)
        )
        
        logger.info(f"Demand letter generation {generation_id} started by user {user.username}")
        
        return GenerationResponse(
            generation_id=generation_id,
            generation_type="demand_letter",
            status="started",
            progress=0,
            started_at=current_time,
            message="Demand letter generation started"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting demand letter generation for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start demand letter generation"
        )


@router.get("/{generation_id}", response_model=GenerationResponse)
async def get_generation(
    generation_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get generation details and content by ID.
    
    Args:
        generation_id: Generation identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        GenerationResponse with generation details and content
    """
    try:
        session_data = await get_current_session(request)
        generations = session_data.get("generations", {})
        
        if generation_id not in generations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generation not found"
            )
        
        generation_data = generations[generation_id]
        
        return GenerationResponse(
            generation_id=generation_id,
            generation_type=generation_data["type"],
            status=generation_data["status"],
            content=generation_data.get("content"),
            export_url=generation_data.get("export_url"),
            progress=generation_data["progress"],
            started_at=generation_data["started_at"],
            completed_at=generation_data.get("completed_at"),
            message="Generation retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting generation {generation_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve generation"
        )


@router.get("/{generation_id}/progress", response_model=ProgressResponse)
async def get_generation_progress(
    generation_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get generation progress.
    
    Args:
        generation_id: Generation identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        ProgressResponse with generation progress
    """
    try:
        session_data = await get_current_session(request)
        generations = session_data.get("generations", {})
        
        if generation_id not in generations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generation not found"
            )
        
        generation_data = generations[generation_id]
        
        return ProgressResponse(
            task_id=generation_id,
            status=generation_data["status"],
            progress=generation_data["progress"],
            message=f"Generation {generation_data['status']}",
            started_at=generation_data["started_at"],
            updated_at=time.time()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting progress for generation {generation_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get generation progress"
        )


@router.get("/{generation_id}/export/word")
async def export_generation_word(
    generation_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Export generation content as Word document.
    
    Args:
        generation_id: Generation identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        Word document as streaming response
    """
    try:
        session_data = await get_current_session(request)
        generations = session_data.get("generations", {})
        
        if generation_id not in generations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generation not found"
            )
        
        generation_data = generations[generation_id]
        
        if generation_data["status"] != "completed" or not generation_data.get("content"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Generation not completed or no content available"
            )
        
        # TODO: Implement Word document generation
        # For now, return plain text
        content = generation_data["content"]
        
        # Create a simple text file (in production, this would be a proper Word document)
        file_content = content.encode('utf-8')
        file_stream = io.BytesIO(file_content)
        
        filename = f"demand_letter_{generation_id[:8]}.txt"
        
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting generation {generation_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export generation"
        )


@router.delete("/{generation_id}", response_model=BaseResponse)
async def delete_generation(
    generation_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Delete a generation.
    
    Args:
        generation_id: Generation identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        BaseResponse confirming deletion
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)
        generations = session_data.get("generations", {})
        
        if generation_id not in generations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generation not found"
            )
        
        # Remove generation from session
        del generations[generation_id]
        
        session_store.update_session(session_id, {
            "generations": generations
        })
        
        logger.info(f"Generation {generation_id} deleted by user {user.username}")
        
        return BaseResponse(message="Generation deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting generation {generation_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete generation"
        )


@router.post("/demand-letter/direct")
async def generate_demand_letter_direct(
    request_data: dict,
    user: User = Depends(get_current_user)
):
    """
    Direct demand letter generation endpoint that processes configuration from frontend.
    Uses the existing CaseBuilder logic and deducts 5 tokens.
    """
    try:
        logger.info(f"Starting direct demand letter generation for user {user.username}")

        # Check and deduct tokens for demand generation
        success, remaining_tokens = await token_service.check_and_deduct_tokens(
            user.username,
            'demand_generation'
        )

        if not success:
            available_tokens = await token_service.get_user_tokens(user.username)
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail=f"Insufficient tokens. Available: {available_tokens}, Required: {token_service.DEMAND_GENERATION_TOKEN_COST}"
            )

        logger.info(f"Deducted {token_service.DEMAND_GENERATION_TOKEN_COST} tokens from user {user.username}. Remaining: {remaining_tokens}")

        # Extract configuration from request
        config = request_data
        client_name = config.get('client_name', 'Client')
        selected_state = config.get('selectedState', 'California')
        case_type = config.get('caseType', 'Traffic Collision')
        liability_type = config.get('liabilityType', 'Bodily Injury Liability')
        demand_amount = config.get('demandAmount', 'Policy Limit')
        custom_amount = config.get('customAmount', '')

        # Get analysis results
        analysis_results = config.get('analysis_results', [])
        current_analysis = config.get('current_analysis', '')

        # Use OpenAI directly with the exact same prompt as CaseBuilder
        from openai import AsyncOpenAI
        import os

        client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        # Create demand letter prompt using CaseBuilder logic
        demand_prompt = f"""
        As an AI legal assistant, draft a comprehensive demand letter for a personal injury case under '{liability_type}' in {selected_state}.

        **Case Information:**
        - Client Name: {client_name}
        - Case Type: {case_type}
        - State: {selected_state}
        - Liability Type: {liability_type}
        - Demand Amount: {demand_amount}{f' ({custom_amount})' if custom_amount else ''}

        **Analysis Results:**
        {current_analysis}

        **Structure the demand letter with the following sections:**
        1. Introduction
        2. Facts
        3. Liability
        4. Damages
        5. Medical Treatment
        6. Impact on Lifestyle
        7. General Damages
        8. Policy Limit Demand
        9. Conclusion

        **Guidelines:**
        - Use professional legal language
        - Be firm but respectful in tone
        - Include specific facts from the analysis
        - Justify the demand amount based on damages
        - Format as a formal business letter
        - Use markdown formatting for better readability

        Generate a complete, professional demand letter ready for submission to the insurance company.
        """

        # Generate demand letter using OpenAI
        response = await client.chat.completions.create(
            model="gpt-4o-2024-05-13",
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert legal assistant specializing in personal injury demand letters. Generate professional, comprehensive demand letters based on case analysis."
                },
                {
                    "role": "user",
                    "content": demand_prompt
                }
            ],
            max_tokens=4000,
            temperature=0.3
        )

        if not response.choices or not response.choices[0].message.content:
            raise Exception("No response from OpenAI API")

        result = response.choices[0].message.content.strip()

        logger.info(f"Demand letter generated successfully for user {user.username}")

        return {
            "status": "completed",
            "result": result,
            "message": "Demand letter generated successfully",
            "tokens_used": token_service.DEMAND_GENERATION_TOKEN_COST,
            "tokens_remaining": remaining_tokens
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating demand letter for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate demand letter: {str(e)}"
        )


@router.post("/export/word")
async def export_generation_word_direct(
    request_data: dict,
    user: User = Depends(get_current_user)
):
    """
    Export generation content as Word document (.docx).
    This endpoint is for direct generation results that aren't stored in session.
    """
    try:
        logger.info(f"Export request received from user {user.username}")

        # Extract data from request
        content = request_data.get("content", "")
        analysis_type = request_data.get("analysis_type", "Demand Letter")
        client_name = request_data.get("client_name", "Client")

        if not content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No content provided for export"
            )

        # Format analysis type for title
        analysis_type_title = analysis_type.replace('_', ' ').title()

        # Create Word document
        from ..services.document_export_service import DocumentExportService
        export_service = DocumentExportService()
        doc_stream = export_service.export_to_word(
            content=content,
            title=f"{analysis_type_title}",
            client_name=client_name,
            analysis_type=analysis_type_title
        )

        # Generate filename
        filename = export_service.get_filename(
            analysis_type=analysis_type_title,
            client_name=client_name
        )

        logger.info(f"Generation exported to Word by user {user.username}")

        return StreamingResponse(
            io.BytesIO(doc_stream.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting generation to Word: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export generation to Word"
        )


async def process_generation_background(
    generation_id: str,
    session_id: str,
    generation_request: GenerationRequest
):
    """
    Background task to process generation request.
    
    Args:
        generation_id: Generation identifier
        session_id: Session identifier
        generation_request: Generation configuration
    """
    try:
        # Get session data
        session_data = session_store.get_session(session_id)
        if not session_data:
            logger.error(f"Session {session_id} not found for generation processing")
            return
        
        generations = session_data.get("generations", {})
        if generation_id not in generations:
            logger.error(f"Generation {generation_id} not found in session")
            return
        
        # Update status to processing
        generations[generation_id]["status"] = "processing"
        generations[generation_id]["progress"] = 10
        session_store.update_session(session_id, {"generations": generations})
        
        # TODO: Integrate with existing CaseBuilder generation logic
        # This would use the existing demand letter generation handlers
        
        # Simulate generation processing
        await asyncio.sleep(2)
        
        # Update progress
        generations[generation_id]["progress"] = 50
        session_store.update_session(session_id, {"generations": generations})
        
        await asyncio.sleep(2)
        
        # Mock generated content
        mock_content = """
DEMAND LETTER

TO: Insurance Company
FROM: Legal Representative
DATE: [Current Date]

RE: Demand for Settlement - [Client Name]

Dear Claims Adjuster,

This letter serves as a formal demand for settlement of the above-referenced claim.

FACTS:
[Analysis results would be inserted here]

LIABILITY:
[Liability analysis would be inserted here]

DAMAGES:
[Damages analysis would be inserted here]

DEMAND:
We demand settlement in the amount of $[Amount] within 30 days.

Sincerely,
[Attorney Name]
        """.strip()
        
        # Update status to completed
        generations[generation_id]["status"] = "completed"
        generations[generation_id]["progress"] = 100
        generations[generation_id]["completed_at"] = time.time()
        generations[generation_id]["content"] = mock_content
        
        # Update session stats
        stats = session_data.get("stats", {})
        stats["generations_completed"] = stats.get("generations_completed", 0) + 1
        
        session_store.update_session(session_id, {
            "generations": generations,
            "stats": stats
        })
        
        logger.info(f"Generation {generation_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Error processing generation {generation_id}: {str(e)}")
        
        # Update status to error
        session_data = session_store.get_session(session_id)
        if session_data:
            generations = session_data.get("generations", {})
            if generation_id in generations:
                generations[generation_id]["status"] = "error"
                generations[generation_id]["error"] = str(e)
                session_store.update_session(session_id, {"generations": generations})
