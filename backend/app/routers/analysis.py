"""
Analysis router for CaseBuilder AI.
Handles document analysis requests and results.
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import StreamingResponse
import logging
import uuid
import time
from typing import List, Optional
import asyncio
import aiohttp
import io

from ..models.requests import AnalysisRequest
from ..models.responses import (
    AnalysisResponse,
    AnalysisListResponse,
    BaseResponse,
    ProgressResponse
)
from pydantic import BaseModel
from ..services.auth_service import User
from ..services.document_export_service import DocumentExportService
from ..services.token_service import token_service
from ..dependencies import get_current_user, get_current_session, validate_session_access
from ..middleware.session import session_store

logger = logging.getLogger(__name__)
router = APIRouter()


# Direct analysis request model
class DirectAnalysisFile(BaseModel):
    name: str
    content: str  # base64 content
    type: str


class DirectAnalysisRequest(BaseModel):
    files: List[DirectAnalysisFile]
    preferences: dict
    analysis_type: str


@router.post("/start", response_model=AnalysisResponse)
async def start_analysis(
    analysis_request: AnalysisRequest,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Start document analysis with specified preferences.
    
    Args:
        analysis_request: Analysis configuration
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisResponse with analysis details
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)

        # Validate that documents exist
        documents = session_data.get("documents", {})
        for doc_id in analysis_request.document_ids:
            if doc_id not in documents:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Document {doc_id} not found"
                )
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        current_time = time.time()
        
        # Create analysis record
        analysis_data = {
            "id": analysis_id,
            "document_ids": analysis_request.document_ids,
            "preferences": analysis_request.preferences.model_dump(),
            "selected_analyses": [a.value for a in analysis_request.selected_analyses],
            "status": "started",
            "progress": 0,
            "results": {},
            "started_at": current_time,
            "completed_at": None,
            "error": None
        }
        
        # Store analysis in session
        analyses = session_data.get("analyses", {})
        analyses[analysis_id] = analysis_data
        
        session_store.update_session(session_id, {
            "analyses": analyses
        })
        
        # Start background analysis
        asyncio.create_task(
            process_analysis_background(analysis_id, session_id, analysis_request)
        )
        
        logger.info(f"Analysis {analysis_id} started by user {user.username}")

        return AnalysisResponse(
            analysis_id=analysis_id,
            analysis_type="multi_analysis",
            status="started",
            progress=0,
            started_at=current_time,
            message="Analysis started successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting analysis for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start analysis"
        )


@router.get("/", response_model=AnalysisListResponse)
async def list_analyses(
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    List all analyses in the current session.
    
    Args:
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisListResponse with list of analyses
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        # Convert to response format
        analysis_list = []
        for analysis_id, analysis_data in analyses.items():
            analysis_list.append(AnalysisResponse(
                analysis_id=analysis_id,
                analysis_type="multi_analysis",
                status=analysis_data["status"],
                progress=analysis_data["progress"],
                started_at=analysis_data["started_at"],
                completed_at=analysis_data.get("completed_at")
            ))
        
        return AnalysisListResponse(
            analyses=analysis_list,
            total_count=len(analysis_list),
            message="Analyses retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error listing analyses for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analyses"
        )


@router.get("/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get analysis details and results by ID.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisResponse with analysis details and results
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        analysis_data = analyses[analysis_id]
        
        # Get results if completed
        result = None
        if analysis_data["status"] == "completed" and analysis_data["results"]:
            # Combine all results into a single string for now
            # In a real implementation, this would be more structured
            result_parts = []
            for analysis_type, analysis_result in analysis_data["results"].items():
                result_parts.append(f"=== {analysis_type.upper()} ===\n{analysis_result}\n")
            result = "\n".join(result_parts)
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            analysis_type="multi_analysis",
            status=analysis_data["status"],
            result=result,
            progress=analysis_data["progress"],
            started_at=analysis_data["started_at"],
            completed_at=analysis_data.get("completed_at"),
            message="Analysis retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis {analysis_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analysis"
        )


@router.get("/{analysis_id}/progress", response_model=ProgressResponse)
async def get_analysis_progress(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get analysis progress.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        ProgressResponse with analysis progress
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        analysis_data = analyses[analysis_id]
        
        return ProgressResponse(
            task_id=analysis_id,
            status=analysis_data["status"],
            progress=analysis_data["progress"],
            message=f"Analysis {analysis_data['status']}",
            started_at=analysis_data["started_at"],
            updated_at=time.time()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting progress for analysis {analysis_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get analysis progress"
        )


@router.delete("/{analysis_id}", response_model=BaseResponse)
async def cancel_analysis(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Cancel or delete an analysis.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        BaseResponse confirming cancellation
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        # Remove analysis from session
        del analyses[analysis_id]
        
        session_store.update_session(session_id, {
            "analyses": analyses
        })
        
        logger.info(f"Analysis {analysis_id} cancelled by user {user.username}")
        
        return BaseResponse(message="Analysis cancelled successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling analysis {analysis_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel analysis"
        )


@router.post("/direct")
async def direct_analysis(
    request_data: DirectAnalysisRequest,
    user: User = Depends(get_current_user)
):
    """
    Direct analysis endpoint that processes files from browser memory.
    Uses the existing CaseBuilder logic and deducts tokens.
    """
    try:
        logger.info(f"Starting direct analysis with {len(request_data.files)} files for user {user.username}")

        # Check and deduct tokens for analysis
        success, remaining_tokens = await token_service.check_and_deduct_tokens(
            user.username,
            'analysis'
        )

        if not success:
            available_tokens = await token_service.get_user_tokens(user.username)
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail=f"Insufficient tokens. Available: {available_tokens}, Required: {token_service.ANALYSIS_TOKEN_COST}"
            )

        logger.info(f"Deducted {token_service.ANALYSIS_TOKEN_COST} tokens from user {user.username}. Remaining: {remaining_tokens}")

        # Use OpenAI directly with the exact same prompt as CaseBuilder
        from openai import AsyncOpenAI
        import os

        client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        logger.info("OpenAI client setup successful")

        # Extract text from files (simplified for testing)
        combined_text = []
        for file_data in request_data.files:
            try:
                logger.info(f"Processing file: {file_data.name}, type: {file_data.type}")

                # Decode base64 content
                import base64
                try:
                    # Handle data URLs (data:type;base64,content)
                    if ',' in file_data.content:
                        file_content = base64.b64decode(file_data.content.split(',')[1])
                    else:
                        file_content = base64.b64decode(file_data.content)

                    logger.info(f"Decoded {len(file_content)} bytes from {file_data.name}")
                except Exception as decode_error:
                    logger.error(f"Base64 decode error for {file_data.name}: {str(decode_error)}")
                    continue

                # Extract text based on file type
                text = ""
                if file_data.type == "application/pdf":
                    # PDF text extraction with detailed logging
                    try:
                        from PyPDF2 import PdfReader
                        from io import BytesIO
                        pdf_file = BytesIO(file_content)
                        pdf_reader = PdfReader(pdf_file)
                        logger.info(f"PDF {file_data.name} has {len(pdf_reader.pages)} pages")

                        text_parts = []
                        for i, page in enumerate(pdf_reader.pages):
                            try:
                                page_text = page.extract_text()
                                logger.info(f"Page {i+1}: extracted {len(page_text)} characters")
                                if page_text.strip():
                                    text_parts.append(page_text)
                                    logger.info(f"Page {i+1}: added {len(page_text.strip())} non-empty characters")
                                else:
                                    logger.warning(f"Page {i+1}: no text content found")
                            except Exception as page_error:
                                logger.error(f"Error extracting text from page {i+1}: {str(page_error)}")

                        text = "\n".join(text_parts)
                        logger.info(f"Total extracted {len(text)} characters from PDF {file_data.name}")

                        # If no text extracted, try OCR with GPT-4o Vision
                        if not text.strip():
                            logger.warning(f"No text extracted from PDF {file_data.name}, trying OCR with GPT-4o Vision")
                            try:
                                # Convert PDF to images and use OCR
                                from pdf2image import convert_from_bytes
                                import base64

                                # Convert PDF to images with optimized settings
                                images = convert_from_bytes(
                                    file_content,
                                    dpi=150,  # Higher DPI for better OCR accuracy
                                    thread_count=2,  # Reduce threads to avoid memory issues
                                    use_cropbox=True,
                                    grayscale=True
                                )

                                # Limit pages for faster processing (first 5 pages for testing)
                                max_pages = min(5, len(images))
                                images = images[:max_pages]
                                logger.info(f"Processing {len(images)} pages (limited to {max_pages}) for OCR")

                                # Process pages in parallel with limited concurrency
                                async def process_page_ocr(i, image):
                                    try:
                                        # Convert PIL image to bytes
                                        img_byte_arr = io.BytesIO()
                                        image.save(img_byte_arr, format='PNG', optimize=True)
                                        img_bytes = img_byte_arr.getvalue()

                                        # Encode to base64
                                        base64_image = base64.b64encode(img_bytes).decode('utf-8')

                                        # Call GPT-4o Vision for OCR
                                        ocr_payload = {
                                            "model": "gpt-4o-2024-05-13",
                                            "messages": [
                                                {
                                                    "role": "user",
                                                    "content": [
                                                        {
                                                            "type": "text",
                                                            "text": "Extract all text from this document page. Maintain structure and formatting. Return only the extracted text."
                                                        },
                                                        {
                                                            "type": "image_url",
                                                            "image_url": {
                                                                "url": f"data:image/png;base64,{base64_image}"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ],
                                            "max_tokens": 3000  # Reduced for faster processing
                                        }

                                        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                                            async with session.post(
                                                "https://api.openai.com/v1/chat/completions",
                                                headers={
                                                    "Content-Type": "application/json",
                                                    "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
                                                },
                                                json=ocr_payload
                                            ) as response:
                                                if response.status == 200:
                                                    response_json = await response.json()
                                                    choices = response_json.get('choices', [])
                                                    if choices:
                                                        page_text = choices[0]['message']['content']
                                                        if page_text.strip():
                                                            logger.info(f"OCR extracted {len(page_text)} characters from page {i+1}")
                                                            return f"=== Page {i+1} ===\n{page_text}"
                                                        else:
                                                            logger.warning(f"OCR found no text on page {i+1}")
                                                            return None
                                                    else:
                                                        logger.warning(f"No OCR response for page {i+1}")
                                                        return None
                                                else:
                                                    logger.error(f"OCR API error for page {i+1}: {response.status}")
                                                    return None
                                    except Exception as page_ocr_error:
                                        logger.error(f"OCR error for page {i+1}: {str(page_ocr_error)}")
                                        return None

                                # Process pages with limited concurrency (2 at a time)
                                semaphore = asyncio.Semaphore(2)

                                async def process_with_semaphore(i, image):
                                    async with semaphore:
                                        return await process_page_ocr(i, image)

                                # Create tasks for all pages
                                tasks = [process_with_semaphore(i, image) for i, image in enumerate(images)]

                                # Wait for all tasks to complete
                                ocr_results = await asyncio.gather(*tasks, return_exceptions=True)

                                # Filter successful results
                                ocr_text_parts = [result for result in ocr_results if isinstance(result, str) and result]

                                if ocr_text_parts:
                                    text = "\n\n".join(ocr_text_parts)
                                    logger.info(f"OCR extracted total {len(text)} characters from PDF {file_data.name}")
                                else:
                                    text = f"[PDF {file_data.name} - both text extraction and OCR failed]"
                                    logger.error(f"Both text extraction and OCR failed for {file_data.name}")

                            except Exception as ocr_error:
                                logger.error(f"OCR processing error for {file_data.name}: {str(ocr_error)}")
                                text = f"[PDF {file_data.name} - text extraction failed, OCR error: {str(ocr_error)}]"

                    except Exception as pdf_error:
                        logger.error(f"PDF extraction error for {file_data.name}: {str(pdf_error)}")
                        text = f"Error extracting PDF text: {str(pdf_error)}"

                elif file_data.type.startswith("text/") or file_data.name.endswith('.txt'):
                    # Text file
                    try:
                        text = file_content.decode('utf-8')
                        logger.info(f"Extracted {len(text)} characters from text file {file_data.name}")
                    except UnicodeDecodeError:
                        try:
                            text = file_content.decode('latin-1')
                            logger.info(f"Extracted {len(text)} characters from text file {file_data.name} (latin-1)")
                        except Exception as text_error:
                            logger.error(f"Text decode error for {file_data.name}: {str(text_error)}")
                            text = f"Could not decode text from {file_data.name}"
                else:
                    # Unknown file type - try as text
                    try:
                        text = file_content.decode('utf-8')
                        logger.info(f"Extracted {len(text)} characters from unknown type {file_data.name}")
                    except:
                        text = f"Unsupported file type: {file_data.type}"

                if text.strip():
                    combined_text.append(f"=== {file_data.name} ===\n{text}\n")
                    logger.info(f"Added text from {file_data.name} to combined text")
                else:
                    logger.warning(f"No text content found in {file_data.name}")

            except Exception as e:
                logger.error(f"Error processing file {file_data.name}: {str(e)}")
                combined_text.append(f"=== {file_data.name} ===\nError processing file: {str(e)}\n")

        if not combined_text:
            logger.warning("No text content extracted from files")
            return {"error": "No text content could be extracted from the uploaded files"}

        document_text = "\n".join(combined_text)
        logger.info(f"Extracted text length: {len(document_text)} characters")

        # Process analysis using exact CaseBuilder prompt
        if request_data.analysis_type == 'police_report_summary':
            logger.info("Processing police report summary using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Use the EXACT prompt from CaseBuilder FactsOfLossHandler
                prompt = f"""
                You are an AI legal expert specializing in personal injury cases.
                Your task is to produce a clear, concise summary of the police report for a traffic collision.

                Provide a balanced analysis with essential details about the accident and liability determination.

                Extract and summarize the following key information:
                1. Date, time, and location of the accident
                2. Parties involved (vehicles, drivers, passengers)
                3. Brief description of how the accident occurred
                4. Weather and road conditions
                5. Reported injuries (if any)
                6. Citations issued (if any)
                7. Officer's determination of fault/liability (if stated in the report)

                For the liability determination (#7), include the officer's opinion if it's stated in the report, but clearly label it as "Officer's Opinion" and add this note: "Note: The officer's opinion on fault is not a legal determination and may not reflect the actual legal liability in this case."
                This is just a factual summary of what's in the report, including the officer's stated conclusions.

                Format your response in a structured, easy-to-read format with clear headings.
                Keep your summary brief and focused on the most important details.

                **Additional Notes (if applicable):**
                {additional_notes}
                """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in personal injury cases."},
                        {"role": "user", "content": f"{prompt}\n\nPolice Report Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'in_depth_liability':
            logger.info("Processing in-depth liability analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Use the EXACT prompt from CaseBuilder DetermineLiabilityHandler with in_depth=True
                prompt = f"""
                You are an AI legal expert specializing in traffic accident liability analysis.

                Conduct a comprehensive, in-depth liability analysis of the provided police report for {client_name}'s case.

                CRITICAL INSTRUCTION: You MUST COMPLETELY IGNORE the officer's opinion on fault or liability. Police officers are not legal experts and their opinions on fault are often incorrect or biased. Your task is to determine liability based SOLELY on the factual evidence and applicable traffic laws.

                IMPORTANT LEGAL PRINCIPLES:
                - In pedestrian-vehicle accidents, drivers have a heightened duty of care toward pedestrians.
                - A pedestrian in a crosswalk with a walk signal has absolute right of way.
                - Drivers must yield to pedestrians in crosswalks, even if the pedestrian entered unexpectedly.
                - Comparative negligence should ONLY be applied when there is CLEAR evidence the pedestrian violated traffic laws.
                - The fact that a collision occurred is itself evidence that the driver failed to maintain proper control of their vehicle.

                Your analysis should focus on determining who is at fault for the accident, with these components:

                **FACTUAL SEQUENCE ANALYSIS**: Analyze the sequence of events leading to the accident based on the factual evidence only. Do not reference or consider the officer's opinion at any point. Apply the correct legal standards based on the type of accident (vehicle-vehicle, vehicle-pedestrian, etc.).

                **WITNESS STATEMENT EVALUATION**: Critically evaluate witness statements for reliability and what they reveal about fault. IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved in the accident (not the drivers or passengers of the vehicles involved). Only if the witness statements are unreliable, mention this fact.

                **TRAFFIC LAW APPLICATION**: Apply relevant traffic laws to determine which party violated applicable regulations. Be specific about which laws were violated and how they apply to this case. Remember that:
                   - Drivers must maintain control of their vehicle at all times
                   - Drivers must maintain a safe following distance
                   - Drivers must yield right of way according to traffic laws
                   - Drivers must exercise extra caution around pedestrians, bicyclists, and in school zones
                   - A driver's duty to avoid a collision supersedes most other considerations

                **PHYSICAL EVIDENCE ASSESSMENT**: Analyze vehicle damage patterns, road conditions, and other physical evidence that indicates fault.

                **PRIMARY FAULT DETERMINATION**: Identify which party bears the primary responsibility for the accident.

                   IMPORTANT: Do NOT assign shared responsibility or comparative negligence unless there is CLEAR AND CONVINCING evidence that both parties violated specific traffic laws. The mere occurrence of an accident is not evidence of shared fault. In cases where one party had the right of way (especially pedestrians in crosswalks), that party should almost never be assigned any portion of fault.

                   If you do find clear evidence of shared responsibility, mention this fact without assigning specific percentages.

                **EVIDENCE STRENGTH ASSESSMENT**: Rate the strength of evidence supporting your liability determination (strong, moderate, or weak). Only if the evidence is weak, mention this fact.

                **LIABILITY CONCLUSION**: Provide your final determination of liability with clear reasoning, focusing on who is legally responsible for the accident.

                The primary purpose of this analysis is NOT to provide a detailed narrative, but to establish WHO IS AT FAULT based on a thorough analysis of the evidence.

                FORMATTING REQUIREMENTS:
                - Use **bold headers** for each section as shown above
                - Structure your response with clear sections using the bold headers
                - Keep content organized and professional
                - Use bold formatting for section headers to improve readability

                Additional Notes (if applicable):
                {additional_notes}
                """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in traffic accident liability analysis."},
                        {"role": "user", "content": f"{prompt}\n\nPolice Report Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI in-depth liability analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'medical_analysis':
            logger.info("Processing medical analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Get analysis configuration from preferences
                analysis_config = prefs.get('analysis', {})
                detail_level = analysis_config.get('detail_level', 'standard').lower()
                content_emphasis = analysis_config.get('content_emphasis', 'medical').lower()
                analysis_style = analysis_config.get('analysis_style', 'narrative').lower()

                # Map frontend values to backend values
                style_mapping = {
                    'narrative': 'narrative',
                    'chronological': 'chronological',
                    'bulleted': 'bulleted',
                    'tabular': 'tabular'
                }
                style = style_mapping.get(analysis_style, 'narrative')

                detail_mapping = {
                    'basic': 'brief',
                    'standard': 'moderate',
                    'comprehensive': 'comprehensive'
                }
                detail = detail_mapping.get(detail_level, 'moderate')

                # Create style instructions based on selected style
                style_instructions = {
                    "narrative": "Present the medical analysis in a structured narrative format with clear sections using **bold headers**. Organize information logically and create a professional medical analysis report.",
                    "chronological": "Organize the medical analysis in chronological order, listing each medical event by date with associated details using **bold headers** for each time period.",
                    "bulleted": "Present the medical analysis as bulleted points for clarity, grouping by provider or category with **bold headers** for each section.",
                    "tabular": "Present the medical analysis in a structured table format where appropriate, with **bold headers** for each section."
                }.get(style, "Present the medical analysis in a structured format with **bold headers**.")

                # Create detail level instructions
                detail_instructions = {
                    "brief": "Provide a concise summary of the most important medical information. Focus only on key injuries, major treatments, and significant findings. Omit minor details and routine visits.",
                    "moderate": "Provide a balanced analysis with essential details. Include all significant medical events and treatments while summarizing routine care. Focus on information relevant to the case.",
                    "comprehensive": "Provide an exhaustive analysis with all available medical details. Include every treatment, visit, medication, and finding from the records. Be thorough and complete in your analysis."
                }.get(detail, "Provide a balanced analysis with essential details.")

                # ICD instruction (always include for medical analysis)
                icd_instruction = "If ICD diagnosis codes are present in the records, include them in your analysis with appropriate context."

                # Use structured prompt with professional formatting
                if style == "narrative":
                    prompt = f"""
                    As an AI medical analyst, your task is to provide a comprehensive medical analysis for {client_name}.

                    Structure your analysis with the following sections using **bold headers**:

                    **INJURY OVERVIEW**: Provide a summary of the primary injuries sustained and their immediate impact.

                    **INITIAL MEDICAL TREATMENT**: Detail the emergency care and initial medical interventions received.

                    **ONGOING MEDICAL CARE**: Describe the continuing treatment, follow-up visits, and medical management.

                    **DIAGNOSTIC FINDINGS**: Summarize key diagnostic tests, imaging results, and clinical findings.

                    **TREATMENT RESPONSE AND PROGRESS**: Analyze the patient's response to treatment and recovery progress.

                    **CURRENT MEDICAL STATUS**: Describe the current condition and any ongoing medical needs.

                    **IMPACT ON DAILY LIFE**: Assess how the injuries have affected the patient's daily activities and quality of life.

                    {detail_instructions}

                    FORMATTING REQUIREMENTS:
                    - Use **bold headers** for each section as shown above
                    - Structure your response with clear sections using the bold headers
                    - Keep content organized and professional
                    - Use bold formatting for section headers to improve readability

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """
                else:
                    prompt = f"""
                    As an AI medical analyst, your task is to provide a comprehensive medical analysis for {client_name}.

                    {style_instructions}

                    {detail_instructions}

                    Structure your analysis with appropriate sections using **bold headers** for:
                    - Injury Overview
                    - Initial Medical Treatment
                    - Ongoing Medical Care
                    - Diagnostic Findings
                    - Treatment Response and Progress
                    - Current Medical Status
                    - Impact on Daily Life

                    FORMATTING REQUIREMENTS:
                    - Use **bold headers** for each section
                    - Structure your response with clear sections
                    - Keep content organized and professional
                    - Use bold formatting for section headers to improve readability

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are an AI medical analyst specializing in personal injury cases."},
                        {"role": "user", "content": f"{prompt}\n\nMedical Records Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI medical analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'medical_expenses':
            logger.info("Processing medical expenses analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Get analysis configuration from preferences
                analysis_config = prefs.get('analysis', {})
                detail_level = analysis_config.get('detail_level', 'standard').lower()
                analysis_style = analysis_config.get('analysis_style', 'tabular').lower()

                # Map frontend values to backend values
                style_mapping = {
                    'narrative': 'narrative',
                    'chronological': 'chronological',
                    'bulleted': 'bulleted',
                    'tabular': 'tabular'
                }
                style = style_mapping.get(analysis_style, 'tabular')

                detail_mapping = {
                    'basic': 'brief',
                    'standard': 'moderate',
                    'comprehensive': 'comprehensive'
                }
                detail = detail_mapping.get(detail_level, 'moderate')

                # Create style instructions
                style_instructions = {
                    'narrative': 'Provide the medical expenses analysis in a structured narrative format with clear sections using **bold headers**. Organize costs and provider information professionally.',
                    'chronological': 'Organize the medical expenses analysis in chronological order, listing each expense event by date with **bold headers** for each time period.',
                    'bulleted': 'Present the medical expenses analysis as bulleted points for clarity, grouping by provider or category with **bold headers** for each section.',
                    'tabular': 'Present the medical expenses in a clear table format with **bold headers** for each section.'
                }.get(style, 'Present the medical expenses in a clear table format with **bold headers**.')

                # Create detail level instructions
                detail_instructions = {
                    "brief": "Provide a concise summary of only the most significant medical expenses. Group smaller expenses together and focus on major costs.",
                    "moderate": "Provide a balanced analysis of medical expenses with moderate detail. Include all significant costs while summarizing minor expenses.",
                    "comprehensive": "Provide an exhaustive breakdown of all medical expenses with complete detail. Include every charge, no matter how small."
                }.get(detail, "Provide a balanced analysis of medical expenses with moderate detail.")

                # ICD instruction
                icd_instruction = "If ICD diagnosis codes are present in the records, include them in your analysis with appropriate context."

                # Use structured prompt with professional formatting
                if style == "narrative":
                    prompt = f"""
                    As an AI legal expert, your task is to extract and compile medical expenses for {client_name} from the provided documents.

                    Structure your analysis with the following sections using **bold headers**:

                    **EXPENSE SUMMARY**: Provide an overview of total medical expenses and major cost categories.

                    **EMERGENCY CARE COSTS**: Detail expenses related to emergency room visits, ambulance services, and immediate care.

                    **HOSPITAL AND INPATIENT COSTS**: Summarize costs for hospital stays, surgeries, and inpatient treatments.

                    **OUTPATIENT TREATMENT COSTS**: Detail expenses for follow-up visits, outpatient procedures, and specialist consultations.

                    **DIAGNOSTIC AND IMAGING COSTS**: Summarize expenses for X-rays, MRIs, CT scans, and other diagnostic tests.

                    **THERAPY AND REHABILITATION COSTS**: Detail costs for physical therapy, occupational therapy, and rehabilitation services.

                    **MEDICATION AND MEDICAL SUPPLIES**: Summarize pharmaceutical costs and medical equipment expenses.

                    **PROVIDER BREAKDOWN**: Organize expenses by medical provider/facility with treatment summaries and costs.

                    {detail_instructions}

                    FORMATTING REQUIREMENTS:
                    - Use **bold headers** for each section as shown above
                    - Include provider names, treatment periods, and total costs
                    - Reference page numbers where appropriate [Page X]
                    - Keep content organized and professional

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """
                else:
                    prompt = f"""
                    As an AI legal expert, your task is to extract and compile medical expenses for {client_name} from the provided documents.

                    {style_instructions}

                    {detail_instructions}

                    For tabular format, create a well-formatted markdown table with the following columns:
                    | Provider/Facility | Treatment/Procedure | Treatment Period | Total Cost | Page Ref |

                    Structure your response with **bold headers** for:
                    - **EXPENSE SUMMARY**
                    - **DETAILED EXPENSE BREAKDOWN** (table)
                    - **COST ANALYSIS BY CATEGORY**

                    For each medical provider/facility, include:
                    - Provider/Facility Name
                    - Treatment/Procedure Summary (summarized description)
                    - Treatment Period (From – To)
                    - Total Cost
                    - Include page references [Page X] where appropriate

                    FORMATTING REQUIREMENTS:
                    - Use **bold headers** for each section
                    - Ensure tables are properly formatted with markdown syntax
                    - Use | to separate columns
                    - Include header and separator rows
                    - Keep content organized and professional

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in medical expense analysis."},
                        {"role": "user", "content": f"{prompt}\n\nMedical Records Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI medical expenses analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'future_treatment':
            logger.info("Processing future treatment analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Get analysis configuration from preferences
                analysis_config = prefs.get('analysis', {})
                detail_level = analysis_config.get('detail_level', 'standard').lower()
                analysis_style = analysis_config.get('analysis_style', 'tabular').lower()

                # Map frontend values to backend values
                style_mapping = {
                    'narrative': 'narrative',
                    'chronological': 'chronological',
                    'bulleted': 'bulleted',
                    'tabular': 'tabular'
                }
                style = style_mapping.get(analysis_style, 'tabular')

                detail_mapping = {
                    'basic': 'brief',
                    'standard': 'moderate',
                    'comprehensive': 'comprehensive'
                }
                detail = detail_mapping.get(detail_level, 'moderate')

                # Create style instructions
                style_instructions = {
                    'narrative': 'Provide a detailed narrative of the anticipated future medical expenses with clear sections using **bold headers**. Explain their purpose and importance in a structured, professional format.',
                    'chronological': 'Organize the future medical expenses chronologically by anticipated date, with **bold headers** for each time period.',
                    'bulleted': 'Present the future medical expenses as bulleted items for clarity, grouping by type or provider with **bold headers** for each section.',
                    'tabular': 'Present the future medical expenses in a well-structured table format with **bold headers** for each section.'
                }.get(style, 'Present the future medical expenses in a well-structured format with **bold headers**.')

                # Create detail level instructions
                detail_instructions = {
                    "brief": "Focus only on the most significant future medical needs. Provide a concise projection of major anticipated expenses.",
                    "moderate": "Provide a balanced projection of future medical needs with reasonable detail. Include all significant anticipated treatments.",
                    "comprehensive": "Provide an exhaustive projection of all possible future medical needs. Include every potential treatment, no matter how minor."
                }.get(detail, "Provide a balanced projection of future medical needs with reasonable detail.")

                # ICD instruction
                icd_instruction = "If ICD diagnosis codes are present in the records, include them in your analysis with appropriate context."

                # Use structured prompt with professional formatting
                if style == "narrative":
                    prompt = f"""
                    As an AI legal expert, your task is to project foreseeable future medical expenses for {client_name}.

                    Structure your analysis with the following sections using **bold headers**:

                    **FUTURE MEDICAL NEEDS OVERVIEW**: Provide a summary of anticipated ongoing medical requirements based on current conditions.

                    **PHYSICAL THERAPY AND REHABILITATION**: Detail projected physical therapy sessions, frequency, duration, and estimated costs.

                    **PAIN MANAGEMENT TREATMENT**: Analyze ongoing pain management needs, including consultations, medications, and potential procedures.

                    **PSYCHOLOGICAL SUPPORT AND COUNSELING**: Project mental health treatment needs, therapy sessions, and associated costs.

                    **MEDICATION AND PHARMACEUTICAL COSTS**: Estimate ongoing medication needs and annual pharmaceutical expenses.

                    **SPECIALIZED MEDICAL PROCEDURES**: Detail any anticipated injections, surgeries, or specialized treatments.

                    **FOLLOW-UP CARE AND MONITORING**: Project routine follow-up visits, monitoring appointments, and preventive care.

                    **TOTAL PROJECTED COSTS**: Summarize the comprehensive estimated future medical expenses.

                    {detail_instructions}

                    FORMATTING REQUIREMENTS:
                    - Use **bold headers** for each section as shown above
                    - Include frequency, duration, cost per session, and total estimated costs
                    - Structure your response with clear sections
                    - Keep content organized and professional

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """
                else:
                    prompt = f"""
                    As an AI legal expert, your task is to project foreseeable future medical expenses for {client_name}.

                    {style_instructions}

                    {detail_instructions}

                    Structure your response with **bold headers** for:
                    - **FUTURE MEDICAL NEEDS OVERVIEW**
                    - **DETAILED EXPENSE PROJECTIONS** (table format)
                    - **COST ANALYSIS BY CATEGORY**
                    - **TOTAL PROJECTED COSTS**

                    For tabular format, create a well-formatted markdown table with the following columns:
                    | Future Medical Expense | Frequency & Duration | Cost per Session | Total Estimated Cost |

                    Your analysis should identify:
                    - Future Medical Expense
                    - Frequency and Duration
                    - Cost per Session
                    - Total Estimated Cost

                    FORMATTING REQUIREMENTS:
                    - Use **bold headers** for each section
                    - Ensure tables are properly formatted with markdown syntax
                    - Use | to separate columns
                    - Include header and separator rows
                    - Keep content organized and professional

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in future medical expense projection."},
                        {"role": "user", "content": f"{prompt}\n\nMedical Records Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI future treatment analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'facts_liability':
            logger.info("Processing facts and liability analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Use the EXACT prompt from CaseBuilder FactsOfLossHandler (standard prompt, not summary_only)
                prompt = f"""
                You are an AI legal expert specializing in personal injury cases.
                Your task is to produce a clear, objective narrative of the circumstances
                leading to the traffic collision based solely on the provided police report.

                Structure your analysis with the following sections using **bold headers**:

                **INCIDENT OVERVIEW**: Provide a brief summary of the incident including date, time, location, and parties involved.

                **CIRCUMSTANCES LEADING TO THE ACCIDENT**: Detail the events and conditions that led up to the collision, including traffic patterns, weather conditions, and any relevant background information.

                **ACCIDENT SEQUENCE**: Provide a detailed description of how the accident occurred, step by step, based on the evidence in the police report.

                **IMMEDIATE AFTERMATH AND RESPONSE**: Describe what happened immediately after the collision, including emergency response, medical attention, and initial actions taken by the parties involved.

                **ENVIRONMENTAL FACTORS**: Analyze any relevant environmental conditions such as weather, road conditions, lighting, traffic signals, and visibility that may have contributed to the accident.

                **WITNESS ACCOUNTS**: Evaluate witness statements for reliability and what they reveal about the accident. Include their observations and assess their credibility.

                **PHYSICAL EVIDENCE AND DAMAGE PATTERNS**: Analyze vehicle damage patterns, road conditions, and other physical evidence that provides insight into how the accident occurred.

                **CITATIONS AND VIOLATIONS**: Document any citations issued or traffic violations noted in the police report, and explain their relevance to the case.

                Focus on creating a comprehensive factual foundation that can be used for liability analysis and legal proceedings. Maintain objectivity and avoid speculation beyond what the evidence supports.

                FORMATTING REQUIREMENTS:
                - Use **bold headers** for each section as shown above
                - Structure your response with clear sections using the bold headers
                - Keep content organized and professional
                - Use bold formatting for section headers to improve readability

                Additional Notes (if applicable):
                {additional_notes}
                """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in personal injury cases."},
                        {"role": "user", "content": f"{prompt}\n\nPolice Report Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI facts and liability analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type in ['analyze_injuries', 'accident_scene', 'property_damage']:
            logger.info(f"Processing {request_data.analysis_type} using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Check if we have images
                image_files = [f for f in request_data.files if f.type.startswith('image/')]
                pdf_files = [f for f in request_data.files if f.type == 'application/pdf']

                if not image_files:
                    result = "No images found for analysis. Please upload images first."
                elif not pdf_files:
                    result = "Image analysis requires at least one PDF document for context. Please upload a PDF document along with your images."
                else:
                    # Get context from PDF documents
                    document_context = document_text

                    # Combine all context information
                    combined_context = f"Document Context: {document_context}\n\nAdditional Notes: {additional_notes}"

                    if request_data.analysis_type == 'analyze_injuries':
                        # Use structured prompt for injury analysis with professional formatting
                        full_instruction = f"""
                        Based on the context provided: {combined_context}, analyze the injuries shown in the image of {client_name}.

                        Structure your analysis with the following sections using **bold headers**:

                        **VISIBLE INJURY ASSESSMENT**: Provide a detailed description of all visible injuries, wounds, bruising, swelling, or other physical trauma evident in the image.

                        **INJURY SEVERITY AND CHARACTERISTICS**: Analyze the apparent severity, extent, and characteristics of the injuries based on visual evidence.

                        **CORRELATION WITH INCIDENT CONTEXT**: Relate the visible injuries to the accident circumstances described in the provided context, explaining how they are consistent with the reported incident.

                        **IMPACT ON DAILY FUNCTIONING**: Assess how these visible injuries would likely affect the client's daily activities, mobility, and quality of life.

                        **LEGAL SIGNIFICANCE**: Explain how these injuries support claims for damages and liability, highlighting their relevance to the case.

                        **MEDICAL TREATMENT IMPLICATIONS**: Discuss what types of medical treatment these injuries would typically require and their potential long-term effects.

                        FORMATTING REQUIREMENTS:
                        - Use **bold headers** for each section as shown above
                        - Structure your response with clear sections
                        - Keep content organized and professional
                        - Focus on objective, factual observations
                        """

                        # Process the first image (for now, we'll handle multiple images later)
                        image_file = image_files[0]

                        # Decode base64 image
                        if ',' in image_file.content:
                            base64_image = image_file.content.split(',')[1]
                        else:
                            base64_image = image_file.content

                        # Call GPT-4o Vision for image analysis
                        response = await client.chat.completions.create(
                            model="gpt-4o-2024-05-13",
                            messages=[
                                {
                                    "role": "user",
                                    "content": [
                                        {
                                            "type": "text",
                                            "text": full_instruction
                                        },
                                        {
                                            "type": "image_url",
                                            "image_url": {
                                                "url": f"data:image/jpeg;base64,{base64_image}"
                                            }
                                        }
                                    ]
                                }
                            ],
                            max_tokens=3000,
                            temperature=0.3
                        )

                        result = response.choices[0].message.content
                        logger.info(f"OpenAI injury analysis completed: {len(result)} characters")

                    elif request_data.analysis_type == 'accident_scene':
                        # Use the EXACT prompt from CaseBuilder app.py for accident scene analysis
                        if len(image_files) > 1:
                            # Multiple images - analyze each and then combine
                            individual_results = []
                            for idx, image_file in enumerate(image_files):
                                image_instruction = f"""
                                Analyze image {idx+1} as part of a sequence capturing the accident scene involving {client_name}.
                                Extract relevant details such as vehicle positions, damages, environmental context, and any clues regarding the sequence of events.
                                """

                                # Decode base64 image
                                if ',' in image_file.content:
                                    base64_image = image_file.content.split(',')[1]
                                else:
                                    base64_image = image_file.content

                                # Call GPT-4o Vision for each image
                                response = await client.chat.completions.create(
                                    model="gpt-4o-2024-05-13",
                                    messages=[
                                        {
                                            "role": "user",
                                            "content": [
                                                {
                                                    "type": "text",
                                                    "text": image_instruction
                                                },
                                                {
                                                    "type": "image_url",
                                                    "image_url": {
                                                        "url": f"data:image/jpeg;base64,{base64_image}"
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    max_tokens=2000,
                                    temperature=0.3
                                )

                                individual_result = response.choices[0].message.content
                                individual_results.append(f"Image {idx+1} Analysis:\n{individual_result}")

                            # Combine individual results
                            combined_input = "\n\n".join(individual_results)

                            # Final consolidation prompt with structured formatting
                            full_instruction = f"""
                            Based on the context provided: {combined_context}, and the individual image analyses below, provide a comprehensive accident scene analysis.

                            Individual Image Analyses:
                            {combined_input}

                            Structure your analysis with the following sections using **bold headers**:

                            **SCENE OVERVIEW**: Provide a comprehensive description of the overall accident scene, including location characteristics and general layout.

                            **VEHICLE POSITIONS AND DAMAGE**: Detail the positions of all vehicles involved and describe visible damage patterns.

                            **ENVIRONMENTAL CONDITIONS**: Analyze road conditions, weather factors, lighting, traffic signals, and visibility conditions.

                            **PHYSICAL EVIDENCE**: Document skid marks, debris patterns, road damage, and other physical evidence visible at the scene.

                            **SEQUENCE OF EVENTS ANALYSIS**: Based on the visual evidence, reconstruct the likely sequence of events leading to and during the collision.

                            **LIABILITY INDICATORS**: Identify visual evidence that supports or contradicts various theories of fault and liability.

                            **SAFETY AND REGULATORY FACTORS**: Note any traffic control devices, signage, road markings, or safety features visible in the scene.

                            FORMATTING REQUIREMENTS:
                            - Use **bold headers** for each section as shown above
                            - Structure your response with clear sections
                            - Keep content organized and professional
                            - Focus on objective, factual observations suitable for legal documentation
                            """

                            # Generate final consolidated analysis
                            response = await client.chat.completions.create(
                                model="gpt-4o",
                                messages=[
                                    {"role": "system", "content": "You are an AI legal expert specializing in accident scene analysis."},
                                    {"role": "user", "content": full_instruction}
                                ],
                                max_tokens=4000,
                                temperature=0.3
                            )

                            result = response.choices[0].message.content
                        else:
                            # Single image analysis
                            image_file = image_files[0]

                            full_instruction = f"""
                            Based on the context provided: {combined_context}, analyze this accident scene image involving {client_name}.

                            Structure your analysis with the following sections using **bold headers**:

                            **SCENE OVERVIEW**: Provide a comprehensive description of the overall accident scene, including location characteristics and general layout.

                            **VEHICLE POSITIONS AND DAMAGE**: Detail the positions of all vehicles involved and describe visible damage patterns.

                            **ENVIRONMENTAL CONDITIONS**: Analyze road conditions, weather factors, lighting, traffic signals, and visibility conditions.

                            **PHYSICAL EVIDENCE**: Document skid marks, debris patterns, road damage, and other physical evidence visible at the scene.

                            **SEQUENCE OF EVENTS ANALYSIS**: Based on the visual evidence, reconstruct the likely sequence of events leading to and during the collision.

                            **LIABILITY INDICATORS**: Identify visual evidence that supports or contradicts various theories of fault and liability.

                            **SAFETY AND REGULATORY FACTORS**: Note any traffic control devices, signage, road markings, or safety features visible in the scene.

                            FORMATTING REQUIREMENTS:
                            - Use **bold headers** for each section as shown above
                            - Structure your response with clear sections
                            - Keep content organized and professional
                            - Focus on objective, factual observations suitable for legal documentation
                            """

                            # Decode base64 image
                            if ',' in image_file.content:
                                base64_image = image_file.content.split(',')[1]
                            else:
                                base64_image = image_file.content

                            # Call GPT-4o Vision for image analysis
                            response = await client.chat.completions.create(
                                model="gpt-4o-2024-05-13",
                                messages=[
                                    {
                                        "role": "user",
                                        "content": [
                                            {
                                                "type": "text",
                                                "text": full_instruction
                                            },
                                            {
                                                "type": "image_url",
                                                "image_url": {
                                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                                }
                                            }
                                        ]
                                    }
                                ],
                                max_tokens=3000,
                                temperature=0.3
                            )

                            result = response.choices[0].message.content

                        logger.info(f"OpenAI accident scene analysis completed: {len(result)} characters")

                    elif request_data.analysis_type == 'property_damage':
                        # Use the EXACT prompt from CaseBuilder app.py for property damage analysis
                        full_instruction = f"""
                        Based on the context provided: {combined_context}, analyze the property damage shown in the images involving {client_name}.

                        Structure your analysis with the following sections using **bold headers**:

                        **DAMAGE OVERVIEW**: Provide a comprehensive summary of all visible property damage, including vehicles and any other affected property.

                        **VEHICLE DAMAGE ASSESSMENT**: Detail the specific damage to each vehicle, including location, extent, and severity of impact damage.

                        **DAMAGE PATTERN ANALYSIS**: Analyze how the damage patterns relate to the reported accident circumstances and sequence of events.

                        **IMPACT FORCE AND DIRECTION**: Assess the apparent force and direction of impact based on damage characteristics and deformation patterns.

                        **PRE-EXISTING VS. ACCIDENT DAMAGE**: Identify and distinguish between pre-existing damage and damage caused by the accident.

                        **REPAIR COST IMPLICATIONS**: Provide estimates or assessments of repair costs, or total loss evaluation where damage severity warrants.

                        **INSURANCE AND LEGAL SIGNIFICANCE**: Explain how the damage supports insurance claims and legal proceedings, including liability determination.

                        FORMATTING REQUIREMENTS:
                        - Use **bold headers** for each section as shown above
                        - Structure your response with clear sections
                        - Keep content organized and professional
                        - Focus on objective, factual observations suitable for insurance claims and legal proceedings
                        """

                        if len(image_files) > 1:
                            # Multiple images - analyze each and then combine
                            individual_results = []
                            for idx, image_file in enumerate(image_files):
                                image_instruction = f"Analyze the property damage visible in image {idx+1}."

                                # Decode base64 image
                                if ',' in image_file.content:
                                    base64_image = image_file.content.split(',')[1]
                                else:
                                    base64_image = image_file.content

                                # Call GPT-4o Vision for each image
                                response = await client.chat.completions.create(
                                    model="gpt-4o-2024-05-13",
                                    messages=[
                                        {
                                            "role": "user",
                                            "content": [
                                                {
                                                    "type": "text",
                                                    "text": image_instruction
                                                },
                                                {
                                                    "type": "image_url",
                                                    "image_url": {
                                                        "url": f"data:image/jpeg;base64,{base64_image}"
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    max_tokens=2000,
                                    temperature=0.3
                                )

                                individual_result = response.choices[0].message.content
                                individual_results.append(f"Image {idx+1} Analysis:\n{individual_result}")

                            # Combine individual results
                            combined_input = "\n\n".join(individual_results)

                            # Generate final consolidated analysis using GPT-4o
                            response = await client.chat.completions.create(
                                model="gpt-4o",
                                messages=[
                                    {"role": "system", "content": "You are an AI expert specializing in property damage assessment."},
                                    {"role": "user", "content": f"{full_instruction}\n\nIndividual Image Analyses:\n{combined_input}"}
                                ],
                                max_tokens=4000,
                                temperature=0.3
                            )

                            result = response.choices[0].message.content
                        else:
                            # Single image analysis
                            image_file = image_files[0]

                            # Decode base64 image
                            if ',' in image_file.content:
                                base64_image = image_file.content.split(',')[1]
                            else:
                                base64_image = image_file.content

                            # Call GPT-4o Vision for image analysis
                            response = await client.chat.completions.create(
                                model="gpt-4o-2024-05-13",
                                messages=[
                                    {
                                        "role": "user",
                                        "content": [
                                            {
                                                "type": "text",
                                                "text": full_instruction
                                            },
                                            {
                                                "type": "image_url",
                                                "image_url": {
                                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                                }
                                            }
                                        ]
                                    }
                                ],
                                max_tokens=3000,
                                temperature=0.3
                            )

                            result = response.choices[0].message.content

                        logger.info(f"OpenAI property damage analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        else:
            result = f"Analysis type {request_data.analysis_type} not yet implemented"

        logger.info(f"Direct analysis completed successfully")

        return {
            "result": result,
            "status": "completed",
            "message": "Analysis completed successfully"
        }

    except Exception as e:
        logger.error(f"Error in direct analysis: {str(e)}")
        return {"error": f"Analysis failed: {str(e)}"}


async def process_analysis_background(
    analysis_id: str, 
    session_id: str, 
    analysis_request: AnalysisRequest
):
    """
    Background task to process analysis request.
    
    Args:
        analysis_id: Analysis identifier
        session_id: Session identifier
        analysis_request: Analysis configuration
    """
    try:
        # Get session data
        session_data = session_store.get_session(session_id)
        if not session_data:
            logger.error(f"Session {session_id} not found for analysis processing")
            return
        
        analyses = session_data.get("analyses", {})
        if analysis_id not in analyses:
            logger.error(f"Analysis {analysis_id} not found in session")
            return
        
        # Update status to processing
        analyses[analysis_id]["status"] = "processing"
        analyses[analysis_id]["progress"] = 10
        session_store.update_session(session_id, {"analyses": analyses})
        
        # Integrate with existing CaseBuilder analysis logic
        from ..services.analysis_service import AnalysisService
        analysis_service = AnalysisService()

        # Process analysis with real CaseBuilder integration
        total_analyses = len(analysis_request.selected_analyses)
        results = {}

        for i, analysis_type in enumerate(analysis_request.selected_analyses):
            # Update progress
            progress = 10 + (80 * (i + 1) // total_analyses)
            analyses[analysis_id]["progress"] = progress
            session_store.update_session(session_id, {"analyses": analyses})

            # Process real analysis
            try:
                result = await analysis_service.process_analysis(
                    analysis_type=analysis_type,
                    document_ids=analysis_request.document_ids,
                    preferences=analysis_request.preferences,
                    session_id=session_id
                )
                results[analysis_type.value] = result
            except Exception as e:
                logger.error(f"Error processing {analysis_type.value}: {str(e)}")
                results[analysis_type.value] = f"Error processing {analysis_type.value}: {str(e)}"
        
        # Update status to completed
        analyses[analysis_id]["status"] = "completed"
        analyses[analysis_id]["progress"] = 100
        analyses[analysis_id]["completed_at"] = time.time()
        analyses[analysis_id]["results"] = results
        
        # Update session stats
        stats = session_data.get("stats", {})
        stats["analyses_completed"] = stats.get("analyses_completed", 0) + 1
        
        session_store.update_session(session_id, {
            "analyses": analyses,
            "stats": stats
        })
        
        logger.info(f"Analysis {analysis_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Error processing analysis {analysis_id}: {str(e)}")
        
        # Update status to error
        session_data = session_store.get_session(session_id)
        if session_data:
            analyses = session_data.get("analyses", {})
            if analysis_id in analyses:
                analyses[analysis_id]["status"] = "error"
                analyses[analysis_id]["error"] = str(e)
                session_store.update_session(session_id, {"analyses": analyses})


@router.get("/{analysis_id}/export/word")
async def export_analysis_word(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Export analysis results as Word document (.docx).

    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user

    Returns:
        StreamingResponse with Word document
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})

        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )

        analysis_data = analyses[analysis_id]

        if analysis_data["status"] != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Analysis not completed yet"
            )

        if not analysis_data.get("results"):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No analysis results found"
            )

        # Combine all results into a single document
        result_parts = []
        for analysis_type, analysis_result in analysis_data["results"].items():
            result_parts.append(f"# {analysis_type.upper().replace('_', ' ')}\n\n{analysis_result}\n\n")

        combined_content = "\n".join(result_parts)

        # Get client name from preferences
        preferences = analysis_data.get("preferences", {})
        client_name = preferences.get("client_name", "Client")

        # Determine analysis type for title
        selected_analyses = analysis_data.get("selected_analyses", [])
        if len(selected_analyses) == 1:
            analysis_type_title = selected_analyses[0].replace('_', ' ').title()
        else:
            analysis_type_title = "Multi-Analysis Report"

        # Create Word document
        export_service = DocumentExportService()
        doc_stream = export_service.export_to_word(
            content=combined_content,
            title=f"{analysis_type_title}",
            client_name=client_name,
            analysis_type=analysis_type_title
        )

        # Generate filename
        filename = export_service.get_filename(
            analysis_type=analysis_type_title,
            client_name=client_name
        )

        logger.info(f"Analysis {analysis_id} exported to Word by user {user.username}")

        return StreamingResponse(
            io.BytesIO(doc_stream.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting analysis {analysis_id} to Word for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export analysis to Word"
        )


@router.post("/export/word")
async def export_direct_analysis_word(
    request_data: dict,
    user: User = Depends(get_current_user)
):
    """
    Export direct analysis results as Word document (.docx).
    This endpoint is for direct analysis results that aren't stored in session.
    """
    try:
        # Extract data from request
        content = request_data.get("content", "")
        analysis_type = request_data.get("analysis_type", "Analysis")
        client_name = request_data.get("client_name", "Client")

        if not content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No content provided for export"
            )

        # Format analysis type for title
        analysis_type_title = analysis_type.replace('_', ' ').title()

        # Create Word document
        from ..services.document_export_service import DocumentExportService
        export_service = DocumentExportService()
        doc_stream = export_service.export_to_word(
            content=content,
            title=f"{analysis_type_title}",
            client_name=client_name,
            analysis_type=analysis_type_title
        )

        # Generate filename
        filename = export_service.get_filename(
            analysis_type=analysis_type_title,
            client_name=client_name
        )

        logger.info(f"Direct analysis exported to Word: {analysis_type}")

        return StreamingResponse(
            io.BytesIO(doc_stream.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting direct analysis to Word: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export analysis to Word"
        )


@router.get("/tokens")
async def get_user_tokens():
    """
    Get the current user's available tokens.
    Simple endpoint that always works.
    """
    try:
        # For now, return working data to get the frontend functional
        # This will be properly implemented once the auth system is stable
        return {
            "tokens_remaining": 2500,
            "analysis_cost": 1,
            "demand_generation_cost": 5,
            "message": "Token information retrieved successfully",
            "username": "demo_user"
        }

    except Exception as e:
        logger.error(f"Error in get_user_tokens: {str(e)}")
        return {
            "tokens_remaining": 2500,
            "analysis_cost": 1,
            "demand_generation_cost": 5,
            "message": "Token information retrieved (fallback)",
            "username": "demo_user",
            "error": str(e)
        }


