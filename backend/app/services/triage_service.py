"""
TriageService for document type detection and classification.

Adapted from CaseBuilder TriageAgent to work with the new backend architecture.
Uses sophisticated keyword scoring to classify documents into types.
"""

import logging
from typing import Dict, Any, List
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

class DocumentType(Enum):
    """Document type enumeration"""
    POLICE_REPORT = "police_report"
    MEDICAL_RECORD = "medical_record"
    BILLING_STATEMENT = "billing_statement"
    DEMAND_LETTER = "demand_letter"
    EVIDENCE = "evidence"
    CORRESPONDENCE = "correspondence"
    OTHER = "other"

class TriageService:
    """
    Service for analyzing and classifying document content.
    
    Uses keyword scoring algorithm to determine document types and provides
    metadata about the document for UI decision making.
    """

    def classify_document(self, content: str, file_name: str = "") -> Dict[str, Any]:
        """
        Classify document content and return type with metadata.
        
        Args:
            content: The document content to analyze
            file_name: Original file name (optional)
            
        Returns:
            Dictionary containing document classification and metadata
        """
        try:
            # Get a comprehensive sample of the document content
            sample_content = self._get_content_sample(content)
            result_lower = sample_content.lower()
            
            # Initialize variables
            doc_type = DocumentType.OTHER
            contains_medical = False
            contains_incident = False
            contains_multiple_types = False
            confidence = 0.5
            
            # Police report keywords with weighted scoring
            police_exclusive_keywords = [
                "police report", "incident report", "accident report", "officer",
                "badge number", "law enforcement", "citation", "patrol car",
                "police department", "traffic collision", "traffic accident",
                "reporting officer", "case number", "incident number", "police narrative",
                "responding officer", "dispatch", "patrol unit", "traffic unit",
                "investigating officer", "police investigation", "traffic report",
                "police officer", "sheriff", "deputy", "highway patrol", "state trooper"
            ]
            
            police_general_keywords = [
                "collision", "traffic", "vehicle", "driver", "passenger",
                "license plate", "intersection", "road", "highway", "accident scene",
                "crash", "impact", "vehicle damage", "traffic signal", "traffic light",
                "speed limit", "lane", "roadway", "traffic violation", "citation"
            ]
            
            # Medical record keywords with weighted scoring
            medical_exclusive_keywords = [
                "medical record", "patient", "diagnosis", "prognosis", "physician",
                "hospital", "clinic", "prescription", "examination", "medical history",
                "chart notes", "soap notes", "assessment", "treatment plan", "icd", "cpt",
                "medical provider", "healthcare", "medical examination", "vitals",
                "patient name", "date of birth", "medical facility", "attending physician",
                "chief complaint", "medical assessment", "discharge summary", "medical report",
                "clinical findings", "medical diagnosis", "patient history", "medical chart",
                "doctor's notes", "nurse", "medical center", "health center", "medical office"
            ]
            
            medical_general_keywords = [
                "treatment", "doctor", "medication", "therapy",
                "surgery", "symptoms", "recovery", "follow-up", "specialist",
                "physical therapy", "rehabilitation", "medical care",
                "prescription", "dosage", "medical procedure", "laboratory",
                "x-ray", "mri", "ct scan", "blood test", "outpatient", "inpatient"
            ]
            
            # Billing statement keywords
            billing_keywords = [
                "bill", "invoice", "charge", "payment", "amount due", "balance",
                "fee", "cost", "service date", "procedure code", "billing", "statement",
                "account number", "insurance claim", "payment due", "total amount",
                "billing date", "service description", "amount charged", "payment method",
                "medical bill", "healthcare bill", "hospital bill", "insurance payment",
                "medical charges", "patient responsibility", "insurance coverage"
            ]
            
            # Common terms that appear in both types (subtract from scores)
            common_terms = [
                "injury", "pain", "accident", "emergency", "hospital visit",
                "hospital", "ambulance", "paramedic", "ems"
            ]
            
            # Calculate weighted scores using exact phrase matching
            police_exclusive_score = sum(4 for keyword in police_exclusive_keywords 
                                       if f" {keyword} " in f" {result_lower} ")
            police_general_score = sum(1 for keyword in police_general_keywords 
                                     if f" {keyword} " in f" {result_lower} ")
            strong_police_indicators = ["badge number", "reporting officer", "investigating officer", "police department"]
            police_strong_score = sum(3 for keyword in strong_police_indicators 
                                    if f" {keyword} " in f" {result_lower} ")
            police_score = police_exclusive_score + police_general_score + police_strong_score
            
            medical_exclusive_score = sum(4 for keyword in medical_exclusive_keywords 
                                        if f" {keyword} " in f" {result_lower} ")
            medical_general_score = sum(1 for keyword in medical_general_keywords 
                                      if f" {keyword} " in f" {result_lower} ")
            strong_medical_indicators = ["chart notes", "soap notes", "medical diagnosis", "attending physician", "icd", "cpt"]
            medical_strong_score = sum(3 for keyword in strong_medical_indicators 
                                     if f" {keyword} " in f" {result_lower} ")
            common_terms_score = sum(1 for keyword in common_terms 
                                   if f" {keyword} " in f" {result_lower} ")
            medical_score = medical_exclusive_score + medical_general_score + medical_strong_score - common_terms_score
            
            billing_score = sum(2 for keyword in billing_keywords 
                              if f" {keyword} " in f" {result_lower} ")
            strong_billing_indicators = ["amount due", "total amount", "insurance claim", "procedure code", "medical bill"]
            billing_strong_score = sum(3 for keyword in strong_billing_indicators 
                                     if f" {keyword} " in f" {result_lower} ")
            billing_score = billing_score + billing_strong_score
            
            logger.info(f"Document scores - Police: {police_score}, Medical: {medical_score}, Billing: {billing_score}")
            
            # Check filename for hints
            file_name_lower = file_name.lower()
            is_likely_police = any(term in file_name_lower for term in 
                                 ["police", "incident", "accident", "collision", "traffic", "crash"])
            is_likely_medical = any(term in file_name_lower for term in 
                                  ["medical", "health", "doctor", "hospital", "clinic", "patient", "treatment"])
            is_likely_billing = any(term in file_name_lower for term in 
                                  ["bill", "invoice", "payment", "statement", "charges"])
            
            # Check for combined document indicators
            combined_indicators = ["exhibit", "combined", "compilation", "multiple", "both", "all documents",
                                 "police and medical", "medical and police", "records and report"]
            is_likely_combined = any(term in file_name_lower for term in combined_indicators)
            
            # Define thresholds based on document length
            document_length = len(result_lower)
            length_factor = min(1.0, max(0.5, document_length / 10000))
            
            police_threshold = int(7 * length_factor)
            medical_threshold = int(8 * length_factor)
            billing_threshold = int(7 * length_factor)
            
            # Adjust thresholds based on filename hints
            if is_likely_police:
                police_threshold = max(4, int(5 * length_factor))
            if is_likely_medical:
                medical_threshold = max(5, int(6 * length_factor))
            if is_likely_billing:
                billing_threshold = max(4, int(5 * length_factor))
            
            # Ensure minimum thresholds
            police_threshold = max(2, police_threshold)
            medical_threshold = max(3, medical_threshold)
            billing_threshold = max(2, billing_threshold)
            
            # Determine content types
            has_police_content = police_score >= police_threshold
            has_medical_content = medical_score >= medical_threshold
            has_billing_content = billing_score >= billing_threshold
            
            # Apply conservative rules for strong documents
            if police_score >= police_threshold * 2:
                has_medical_content = medical_score >= medical_threshold * 2.0
            elif police_score >= police_threshold + 5:
                has_medical_content = medical_score >= medical_threshold + 4
                
            if medical_score >= medical_threshold * 2:
                has_police_content = police_score >= police_threshold * 2.0
            elif medical_score >= medical_threshold + 5:
                has_police_content = police_score >= police_threshold + 4
            
            # For long documents, be more conservative
            if document_length > 5000 and police_score >= 10:
                has_medical_content = False
            if document_length > 5000 and medical_score >= 12:
                has_police_content = False
            
            # Determine multiple types
            document_types_present = sum([
                1 if has_police_content else 0,
                1 if has_medical_content else 0,
                1 if has_billing_content else 0
            ])
            
            has_multiple_strong_scores = (has_police_content and has_medical_content and
                                        police_score >= police_threshold * 1.5 and
                                        medical_score >= medical_threshold * 1.5)
            
            contains_multiple_types = document_types_present > 1 and (is_likely_combined or has_multiple_strong_scores)
            
            # Override for non-combined documents
            if not is_likely_combined:
                if has_police_content and has_medical_content:
                    if police_score > medical_score * 2:
                        has_medical_content = False
                        contains_multiple_types = False
                    elif medical_score > police_score * 2:
                        has_police_content = False
                        contains_multiple_types = False
            
            # Set content flags
            contains_incident = has_police_content or any(keyword in result_lower for keyword in 
                                                        ["incident", "accident", "collision", "crash", "impact"])
            contains_medical = has_medical_content or has_billing_content or any(keyword in result_lower for keyword in 
                                                                               ["diagnosis", "treatment", "injury", "pain", "medication"])
            
            # Make final classification
            if contains_multiple_types:
                max_score = max(police_score, medical_score, billing_score)
                if max_score == police_score and has_police_content:
                    doc_type = DocumentType.POLICE_REPORT
                elif max_score == medical_score and has_medical_content:
                    doc_type = DocumentType.MEDICAL_RECORD
                elif max_score == billing_score and has_billing_content:
                    doc_type = DocumentType.BILLING_STATEMENT
                else:
                    doc_type = DocumentType.OTHER
            else:
                if has_police_content:
                    doc_type = DocumentType.POLICE_REPORT
                elif has_medical_content:
                    doc_type = DocumentType.MEDICAL_RECORD
                elif has_billing_content:
                    doc_type = DocumentType.BILLING_STATEMENT
                else:
                    max_score = max(police_score, medical_score, billing_score)
                    if max_score >= 3:
                        if max_score == police_score:
                            doc_type = DocumentType.POLICE_REPORT
                            contains_incident = True
                        elif max_score == medical_score:
                            doc_type = DocumentType.MEDICAL_RECORD
                            contains_medical = True
                        elif max_score == billing_score:
                            doc_type = DocumentType.BILLING_STATEMENT
                            contains_medical = True
            
            # Calculate confidence
            max_score = max(police_score, medical_score, billing_score)
            base_confidence = min(0.9, 0.4 + (max_score * 0.1))
            
            if contains_multiple_types:
                confidence = max(0.4, base_confidence - 0.1)
            else:
                confidence = base_confidence
            
            # Create suggested processors
            suggested_processors = []
            if contains_incident:
                suggested_processors.append("facts_liability")
            if contains_medical:
                suggested_processors.extend(["medical_analysis", "medical_expenses"])
            
            logger.info(f"Final classification: {doc_type}, confidence: {confidence}")
            
            return {
                "document_type": doc_type.value,
                "confidence": confidence,
                "metadata": {
                    "contains_medical_info": contains_medical,
                    "contains_incident_info": contains_incident,
                    "contains_multiple_types": contains_multiple_types,
                    "document_types_detected": {
                        "police_report": has_police_content,
                        "medical_record": has_medical_content,
                        "billing_statement": has_billing_content
                    },
                    "suggested_processors": suggested_processors,
                    "scores": {
                        "police_score": police_score,
                        "medical_score": medical_score,
                        "billing_score": billing_score
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Error during document classification: {str(e)}")
            return {
                "document_type": DocumentType.OTHER.value,
                "confidence": 0.3,
                "metadata": {
                    "contains_medical_info": False,
                    "contains_incident_info": False,
                    "contains_multiple_types": False,
                    "document_types_detected": {
                        "police_report": False,
                        "medical_record": False,
                        "billing_statement": False
                    },
                    "suggested_processors": [],
                    "error": str(e)
                }
            }

    def _get_content_sample(self, content: str) -> str:
        """
        Extract representative sample from document content for classification.
        """
        if len(content) < 4000:
            return content
        
        doc_length = len(content)
        
        if doc_length > 50000:
            num_samples = 7
            sample_size = 1500
        elif doc_length > 20000:
            num_samples = 5
            sample_size = 1800
        else:
            num_samples = 3
            sample_size = 2000
        
        samples = []
        
        # Beginning sample (larger)
        beginning_sample_size = min(sample_size * 2, doc_length // 3)
        samples.append(content[:beginning_sample_size])
        
        # Intermediate samples
        for i in range(1, num_samples-1):
            position = (doc_length * i) // (num_samples - 1)
            start = max(0, position - (sample_size // 2))
            samples.append(content[start:start + sample_size])
        
        # End sample
        end_start = max(0, doc_length - sample_size)
        samples.append(content[end_start:])
        
        return "\n\n[...]\n\n".join(samples)
