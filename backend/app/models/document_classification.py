"""
Document classification models for the triage system.
"""

from pydantic import BaseModel
from typing import Dict, List, Optional
from enum import Enum

class DocumentType(str, Enum):
    """Document type enumeration for API responses"""
    POLICE_REPORT = "police_report"
    MEDICAL_RECORD = "medical_record"
    BILLING_STATEMENT = "billing_statement"
    DEMAND_LETTER = "demand_letter"
    EVIDENCE = "evidence"
    CORRESPONDENCE = "correspondence"
    OTHER = "other"

class DocumentTypesDetected(BaseModel):
    """Detected document types with boolean flags"""
    police_report: bool = False
    medical_record: bool = False
    billing_statement: bool = False

class ClassificationScores(BaseModel):
    """Keyword matching scores for each document type"""
    police_score: int = 0
    medical_score: int = 0
    billing_score: int = 0

class ClassificationMetadata(BaseModel):
    """Metadata about the document classification"""
    contains_medical_info: bool = False
    contains_incident_info: bool = False
    contains_multiple_types: bool = False
    document_types_detected: DocumentTypesDetected
    suggested_processors: List[str] = []
    scores: Optional[ClassificationScores] = None
    error: Optional[str] = None

class DocumentClassificationRequest(BaseModel):
    """Request model for document classification"""
    content: str
    file_name: Optional[str] = ""
    file_type: Optional[str] = ""

class DocumentClassificationResponse(BaseModel):
    """Response model for document classification"""
    document_type: DocumentType
    confidence: float
    metadata: ClassificationMetadata
    
    class Config:
        use_enum_values = True

class ClassificationSummary(BaseModel):
    """Summary of all uploaded documents classification"""
    total_documents: int = 0
    has_police_reports: bool = False
    has_medical_records: bool = False
    has_billing_statements: bool = False
    has_multiple_types: bool = False
    dominant_type: Optional[DocumentType] = None
    suggested_actions: List[str] = []
